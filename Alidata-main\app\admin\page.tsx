"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, DollarSign, Activity, TrendingUp, Smartphone, Wifi, CreditCard, AlertCircle } from "lucide-react"
import { toast } from "sonner"

interface AdminStats {
  totalUsers: number
  totalBalance: number
  avgBalance: number
  totalTransactions: number
  totalRevenue: number
  pendingTransactions: number
  successfulTransactions: number
  failedTransactions: number
  transactionStatsByType: Array<{
    _id: string
    count: number
    totalAmount: number
  }>
  recentTransactions: Array<{
    _id: string
    type: string
    amount: number
    status: string
    createdAt: string
    userId: {
      firstName: string
      lastName: string
      email: string
    }
  }>
  dailyStats: Array<{
    _id: {
      date: string
      type: string
    }
    count: number
    amount: number
  }>
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAdminStats()
  }, [])

  const fetchAdminStats = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch("/api/admin/dashboard")
      const result = await response.json()

      if (response.ok && result.success) {
        setStats(result.data)
        toast.success(result.message || "Admin dashboard stats loaded.")
      } else {
        setError(result.message || "Failed to fetch admin dashboard stats.")
        toast.error(result.message || "Failed to fetch admin dashboard stats.")
      }
    } catch (err) {
      console.error("Error fetching admin stats:", err)
      setError("An unexpected error occurred while fetching admin stats.")
      toast.error("An unexpected error occurred while fetching admin stats.")
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "success":
        return "bg-green-900/50 text-green-300 border-green-700"
      case "pending":
        return "bg-yellow-900/50 text-yellow-300 border-yellow-700"
      case "failed":
        return "bg-red-900/50 text-red-300 border-red-700"
      default:
        return "bg-slate-700 text-slate-300 border-slate-600"
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "airtime":
        return <Smartphone className="h-4 w-4" />
      case "data":
        return <Wifi className="h-4 w-4" />
      case "wallet_funding":
        return <CreditCard className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading admin dashboard...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="bg-slate-800 border-slate-700 text-white max-w-md w-full">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" /> Error
            </CardTitle>
            <CardDescription className="text-slate-400">{error} Please try refreshing the page.</CardDescription>
          </CardHeader>
          <CardContent>
            <button
              onClick={fetchAdminStats}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition-colors"
            >
              Refresh
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-slate-400">Monitor and manage your VTU platform</p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Users</CardTitle>
              <Users className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalUsers || 0}</div>
              <p className="text-xs text-slate-400">Registered users</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">₦{stats?.totalRevenue?.toLocaleString() || 0}</div>
              <p className="text-xs text-slate-400">All time revenue</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Transactions</CardTitle>
              <Activity className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalTransactions || 0}</div>
              <p className="text-xs text-slate-400">All transactions</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {stats?.totalTransactions
                  ? Math.round((stats.successfulTransactions / stats.totalTransactions) * 100)
                  : 0}
                %
              </div>
              <p className="text-xs text-slate-400">Transaction success rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Transaction Status Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Successful</CardTitle>
              <div className="h-2 w-2 bg-green-400 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-400">{stats?.successfulTransactions || 0}</div>
              <p className="text-xs text-slate-400">Completed transactions</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Pending</CardTitle>
              <div className="h-2 w-2 bg-yellow-400 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-400">{stats?.pendingTransactions || 0}</div>
              <p className="text-xs text-slate-400">Processing transactions</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Failed</CardTitle>
              <div className="h-2 w-2 bg-red-400 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-400">{stats?.failedTransactions || 0}</div>
              <p className="text-xs text-slate-400">Failed transactions</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Recent Transactions</CardTitle>
            <CardDescription className="text-slate-400">Latest platform activity</CardDescription>
          </CardHeader>
          <CardContent>
            {stats?.recentTransactions && stats.recentTransactions.length > 0 ? (
              <div className="space-y-4">
                {stats.recentTransactions.slice(0, 10).map((transaction) => (
                  <div
                    key={transaction._id}
                    className="flex items-center justify-between p-4 border border-slate-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-slate-700 rounded-lg">{getTransactionIcon(transaction.type)}</div>
                      <div>
                        <p className="font-medium text-white">{transaction.type.replace("_", " ").toUpperCase()}</p>
                        <div className="flex items-center space-x-2 text-sm text-slate-400">
                          <span>
                            {transaction.userId?.firstName} {transaction.userId?.lastName}
                          </span>
                          <span>•</span>
                          <span>{new Date(transaction.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-white">₦{transaction.amount.toLocaleString()}</p>
                      <Badge variant="outline" className={getStatusColor(transaction.status)}>
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <AlertCircle className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400 text-lg mb-2">No transactions yet</p>
                <p className="text-slate-500">Transaction activity will appear here</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
