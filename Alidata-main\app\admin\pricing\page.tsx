"use client"

import { useState, useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Edit, CheckCircle, XCircle, Database } from "lucide-react"
import { toast } from "sonner"

interface DataPlan {
  _id: string
  network: string
  planId: string
  planName: string
  serviceType: string
  serviceCode: string
  dataSize: string
  validity: string
  apiPrice: number
  sellingPrice: number
  isActive: boolean
}

const networks = [
  { value: "all", label: "All Networks" },
  { value: "mtn", label: "MTN" },
  { value: "airtel", label: "Airtel" },
  { value: "glo", label: "Glo" },
  { value: "9mobile", label: "9mobile" },
]

const serviceTypes = [
  { value: "all", label: "All Service Types" },
  { value: "SME", label: "SME Data" },
  { value: "GIFT", label: "Gifting Data" },
  { value: "CG", label: "Corporate Gifting" }, // Added CG based on model
  { value: "AWOOF", label: "Awoof Data" },
]

export default function AdminPricingPage() {
  const { data: session, status } = useSession()
  const [plans, setPlans] = useState<DataPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedNetwork, setSelectedNetwork] = useState("all")
  const [selectedServiceType, setSelectedServiceType] = useState("all")
  const [editingPlan, setEditingPlan] = useState<DataPlan | null>(null)
  const [message, setMessage] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()

  const fetchPlans = useCallback(async () => {
    try {
      setLoading(true)
      setError("") // Clear previous errors
      const params = new URLSearchParams()
      if (selectedNetwork !== "all") params.append("network", selectedNetwork)
      if (selectedServiceType !== "all") params.append("serviceType", selectedServiceType)

      const response = await fetch(`/api/data-plans?${params.toString()}`)
      const result = await response.json()

      if (response.ok && result.success) {
        setPlans(result.data || [])
        setMessage(result.message || "Data plans loaded.")
      } else {
        setError(result.message || "Failed to fetch data plans")
        toast.error("Failed to fetch data plans", {
          description: result.message || "Please try again later",
          duration: 4000,
        })
        setPlans([]) // Clear plans on error
      }
    } catch (err) {
      console.error("Error fetching plans:", err)
      setError("An error occurred while fetching plans")
      toast.error("Network error", {
        description: "Please check your connection",
        duration: 4000,
      })
      setPlans([]) // Clear plans on network error
    } finally {
      setLoading(false)
    }
  }, [selectedNetwork, selectedServiceType])

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
      return
    }

    if (status === "authenticated" && session?.user?.role !== "admin") {
      router.push("/dashboard")
      return
    }

    if (status === "authenticated" && session?.user?.role === "admin") {
      fetchPlans()
    }
  }, [status, session, router, fetchPlans]) // Added fetchPlans to dependency array

  const updatePlan = async (planId: string, sellingPrice: number) => {
    if (!editingPlan) return

    try {
      setMessage("") // Clear previous messages
      setError("") // Clear previous errors
      const response = await fetch("/api/admin/pricing", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          network: editingPlan.network,
          planId: editingPlan.planId,
          planName: editingPlan.planName,
          serviceType: editingPlan.serviceType,
          serviceCode: editingPlan.serviceCode,
          dataSize: editingPlan.dataSize,
          apiPrice: editingPlan.apiPrice,
          sellingPrice,
          validity: editingPlan.validity,
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setMessage(result.message || "Plan updated successfully")
        setEditingPlan(null)
        fetchPlans() // Re-fetch plans to show updated price
        toast.success("Plan updated successfully! 🎉", {
          description: `${editingPlan.planName} price updated to ₦${sellingPrice.toLocaleString()}`,
          duration: 3000,
        })
      } else {
        setError(result.message || "Failed to update plan")
        toast.error("Update failed", {
          description: result.message || "Please try again",
          duration: 4000,
        })
      }
    } catch (err) {
      console.error("Error updating plan:", err)
      setError("An error occurred while updating the plan")
      toast.error("Network error", {
        description: "Please check your connection",
        duration: 4000,
      })
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Database className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <div className="text-white">Loading pricing data...</div>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "admin") {
    // This case should ideally be handled by the useEffect redirect,
    // but as a fallback, return null or a message.
    return null
  }

  const getNetworkColor = (network: string) => {
    switch (network?.toLowerCase()) {
      case "mtn":
        return "bg-yellow-900/50 text-yellow-300 border-yellow-700"
      case "airtel":
        return "bg-red-900/50 text-red-300 border-red-700"
      case "glo":
        return "bg-green-900/50 text-green-300 border-green-700"
      case "9mobile":
        return "bg-blue-900/50 text-blue-300 border-blue-700"
      default:
        return "bg-slate-700 text-slate-300 border-slate-600"
    }
  }

  const getServiceTypeColor = (serviceType: string) => {
    switch (serviceType) {
      case "SME":
        return "bg-blue-900/50 text-blue-300 border-blue-700"
      case "GIFT":
        return "bg-purple-900/50 text-purple-300 border-purple-700"
      case "CG": // Added CG
        return "bg-orange-900/50 text-orange-300 border-orange-700"
      case "AWOOF":
        return "bg-pink-900/50 text-pink-300 border-pink-700"
      default:
        return "bg-slate-700 text-slate-300 border-slate-600"
    }
  }

  const filteredPlans = plans.filter((plan) => {
    const networkMatch = selectedNetwork === "all" || plan.network?.toLowerCase() === selectedNetwork.toLowerCase()
    const serviceTypeMatch = selectedServiceType === "all" || plan.serviceType === selectedServiceType
    return networkMatch && serviceTypeMatch
  })

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-7xl">
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">Data Pricing</h1>
              <p className="text-slate-400 text-sm sm:text-base">Manage data plan pricing</p>
            </div>
            <Link href="/admin">
              <Button
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700 w-fit bg-transparent"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Admin
              </Button>
            </Link>
          </div>
        </div>

        {/* Messages */}
        {message && (
          <Alert className="mb-4 sm:mb-6 bg-green-900/50 border-green-700">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-300">{message}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert className="mb-4 sm:mb-6 bg-red-900/50 border-red-700">
            <XCircle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-300">{error}</AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <Card className="mb-4 sm:mb-6 bg-slate-800 border-slate-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-white text-lg sm:text-xl">Filter Plans</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="network" className="text-slate-300 text-sm">
                  Network
                </Label>
                <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="All networks" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {networks.map((network) => (
                      <SelectItem key={network.value} value={network.value}>
                        {network.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="serviceType" className="text-slate-300 text-sm">
                  Service Type
                </Label>
                <Select value={selectedServiceType} onValueChange={setSelectedServiceType}>
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="All service types" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {serviceTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Plans Table */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="pb-4">
            <CardTitle className="text-white text-lg sm:text-xl">Data Plans ({filteredPlans.length})</CardTitle>
            <CardDescription className="text-slate-400 text-sm">Manage data plan pricing</CardDescription>
          </CardHeader>
          <CardContent>
            {filteredPlans.length > 0 ? (
              <div className="space-y-3 sm:space-y-4">
                {filteredPlans.map((plan) => (
                  <div
                    key={plan._id}
                    className="flex flex-col sm:flex-row sm:items-center justify-between p-3 sm:p-4 border border-slate-700 rounded-lg bg-slate-800/50"
                  >
                    <div className="flex-1 mb-3 sm:mb-0">
                      <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                        <div className="flex space-x-2">
                          <Badge className={getNetworkColor(plan.network)} variant="outline">
                            {plan.network?.toUpperCase() || "N/A"}
                          </Badge>
                          {plan.serviceType && (
                            <Badge className={getServiceTypeColor(plan.serviceType)} variant="outline">
                              {plan.serviceType}
                            </Badge>
                          )}
                        </div>
                        <div className="min-w-0">
                          <p className="font-medium text-white text-sm sm:text-base truncate">{plan.planName}</p>
                          <p className="text-xs sm:text-sm text-slate-400">
                            {plan.dataSize} {plan.validity && `• ${plan.validity}`}
                          </p>
                          <p className="text-xs text-slate-500">ID: {plan.planId}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                      <div className="text-right">
                        <div className="space-y-1">
                          <p className="text-xs sm:text-sm text-slate-400">
                            Cost: ₦{(plan.apiPrice || 0).toLocaleString()}
                          </p>
                          <p className="font-medium text-green-400 text-sm sm:text-base">
                            ₦{(plan.sellingPrice || 0).toLocaleString()}
                          </p>
                          <p className="text-xs sm:text-sm text-blue-400">
                            +₦{((plan.sellingPrice || 0) - (plan.apiPrice || 0)).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingPlan(plan)}
                        className="border-slate-600 text-slate-300 hover:bg-slate-700 w-full sm:w-auto"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-lg text-slate-300">No data plans found</p>
                <p className="text-sm text-slate-400">Try adjusting your filters or check if plans are seeded</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Plan Modal */}
        {editingPlan && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-md bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Edit Selling Price</CardTitle>
                <CardDescription className="text-slate-400">
                  {editingPlan.planName} ({editingPlan.serviceType || "N/A"})
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form
                  onSubmit={(e) => {
                    e.preventDefault()
                    const formData = new FormData(e.currentTarget)
                    const sellingPrice = Number.parseFloat(formData.get("sellingPrice") as string)
                    updatePlan(editingPlan._id, sellingPrice)
                  }}
                  className="space-y-4"
                >
                  <div>
                    <Label className="text-slate-300">Cost Price</Label>
                    <Input
                      value={`₦${(editingPlan.apiPrice || 0).toLocaleString()}`}
                      disabled
                      className="bg-slate-700 border-slate-600 text-slate-400"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sellingPrice" className="text-slate-300">
                      Your Selling Price
                    </Label>
                    <Input
                      id="sellingPrice"
                      name="sellingPrice"
                      type="number"
                      defaultValue={editingPlan.sellingPrice || 0}
                      min={editingPlan.apiPrice || 0} // Ensure selling price is not less than API price
                      required
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                    <p className="text-xs text-slate-500 mt-1">
                      Minimum: ₦{(editingPlan.apiPrice || 0).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setEditingPlan(null)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                      Update Price
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
