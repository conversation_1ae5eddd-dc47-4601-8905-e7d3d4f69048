import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    // Get user statistics
    const userStatsResult = await User.aggregate([
      { $match: { role: "user" } },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          totalBalance: { $sum: "$balance" },
          avgBalance: { $avg: "$balance" },
        },
      },
    ])
    const userStats = userStatsResult[0] || { totalUsers: 0, totalBalance: 0, avgBalance: 0 }

    // Get overall transaction statistics
    const totalTransactions = await Transaction.countDocuments()
    const successfulTransactions = await Transaction.countDocuments({ status: "completed" })
    const pendingTransactions = await Transaction.countDocuments({ status: "pending" })
    const failedTransactions = await Transaction.countDocuments({ status: "failed" })

    const totalRevenueResult = await Transaction.aggregate([{ $group: { _id: null, total: { $sum: "$amount" } } }])
    const totalRevenue = totalRevenueResult.length > 0 ? totalRevenueResult[0].total : 0

    // Get transaction statistics by type (for breakdown)
    const transactionStatsByType = await Transaction.aggregate([
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ])

    // Get recent transactions
    const recentTransactions = await Transaction.find()
      .populate("userId", "firstName lastName email")
      .sort({ createdAt: -1 })
      .limit(10)

    // Get daily transaction stats for the last 7 days
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const dailyStats = await Transaction.aggregate([
      { $match: { createdAt: { $gte: sevenDaysAgo } } },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            type: "$type",
          },
          count: { $sum: 1 },
          amount: { $sum: "$amount" },
        },
      },
      { $sort: { "_id.date": 1 } },
    ])

    return NextResponse.json({
      success: true,
      message: "Admin dashboard stats fetched successfully",
      data: {
        totalUsers: userStats.totalUsers,
        totalBalance: userStats.totalBalance,
        avgBalance: userStats.avgBalance,
        totalTransactions,
        successfulTransactions,
        pendingTransactions,
        failedTransactions,
        totalRevenue,
        transactionStatsByType, // This is the breakdown by type
        recentTransactions,
        dailyStats,
      },
    })
  } catch (error) {
    console.error("Admin dashboard error:", error)
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 })
  }
}
