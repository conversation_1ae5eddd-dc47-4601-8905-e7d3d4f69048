import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import DataPricing from "@/models/DataPricing"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    const { searchParams } = new URL(request.url)
    const network = searchParams.get("network")

    const query = network ? { network } : {}
    const pricing = await DataPricing.find(query).sort({ network: 1, planName: 1 })

    return NextResponse.json({ pricing })
  } catch (error) {
    console.error("Admin pricing error:", error)
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    const { network, planId, planName, apiPrice, sellingPrice, validity, serviceType, serviceCode, dataSize } =
      await request.json()

    // Find and update the existing plan or create a new one
    const pricing = await DataPricing.findOneAndUpdate(
      { network, planId, serviceType }, // Use serviceType in the query for better uniqueness
      {
        network,
        planId,
        planName,
        serviceType,
        serviceCode,
        dataSize,
        apiPrice,
        sellingPrice,
        validity,
        isActive: true,
      },
      { upsert: true, new: true }, // upsert: true creates if not found, new: true returns the updated document
    )

    return NextResponse.json({ success: true, message: "Pricing updated successfully", pricing })
  } catch (error) {
    console.error("Admin pricing create/update error:", error)
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 })
  }
}
