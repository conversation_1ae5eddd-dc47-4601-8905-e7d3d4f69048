import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import DataPricing from "@/models/DataPricing"
import { logger } from "@/lib/logger"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    logger.info("Starting data plans sync from VTU Africa API", {
      adminId: session.user.id,
    })

    // Get actual VTU Africa data plans from their documentation
    const vtuAfricaPlans = getVTUAfricaDataPlans()

    const updatedPlans = []

    for (const plan of vtuAfricaPlans) {
      const existingPlan = await DataPricing.findOne({
        network: plan.network,
        planId: plan.planId,
        serviceType: plan.serviceType,
      })

      // Calculate selling price with markup (default 5% above reseller price)
      const sellingPrice = existingPlan?.sellingPrice || Math.round(plan.resellerPrice * 1.05)

      const updatedPlan = await DataPricing.findOneAndUpdate(
        {
          network: plan.network,
          planId: plan.planId,
          serviceType: plan.serviceType,
        },
        {
          network: plan.network,
          planId: plan.planId,
          planName: plan.planName,
          serviceType: plan.serviceType,
          serviceCode: plan.serviceCode,
          apiPrice: plan.resellerPrice, // Use reseller price as our cost
          sellingPrice: sellingPrice,
          validity: plan.validity,
          dataSize: plan.dataSize,
          isActive: plan.status === "Active",
          vtuPricing: {
            portalOwner: plan.portalOwnerPrice,
            reseller: plan.resellerPrice,
            agent: plan.agentPrice,
            freeUser: plan.freeUserPrice,
          },
        },
        { upsert: true, new: true },
      )

      updatedPlans.push(updatedPlan)
    }

    logger.info("Data plans sync completed successfully", {
      adminId: session.user.id,
      updatedPlansCount: updatedPlans.length,
    })

    return NextResponse.json({
      message: "Data plans synced successfully from VTU Africa",
      updatedPlans: updatedPlans.length,
    })
  } catch (error) {
    logger.error("Admin pricing sync error", error)
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}

// Actual VTU Africa data plans from their official documentation
function getVTUAfricaDataPlans() {
  return [
    // MTN SME Data Plans
    {
      network: "mtn",
      planId: "500W",
      planName: "MTN SME Data 500MB",
      serviceType: "SME",
      serviceCode: "MTNSME",
      dataSize: "500MB",
      validity: "7-Days",
      portalOwnerPrice: 490,
      resellerPrice: 495,
      agentPrice: 500,
      freeUserPrice: 505,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "1000",
      planName: "MTN SME Data 1GB",
      serviceType: "SME",
      serviceCode: "MTNSME",
      dataSize: "1GB",
      validity: "30-Days",
      portalOwnerPrice: 1540,
      resellerPrice: 1545,
      agentPrice: 1550,
      freeUserPrice: 1555,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "3000",
      planName: "MTN SME Data 3GB",
      serviceType: "SME",
      serviceCode: "MTNSME",
      dataSize: "3GB",
      validity: "30-Days",
      portalOwnerPrice: 2310,
      resellerPrice: 2315,
      agentPrice: 2320,
      freeUserPrice: 2325,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "5000",
      planName: "MTN SME Data 5GB",
      serviceType: "SME",
      serviceCode: "MTNSME",
      dataSize: "5GB",
      validity: "30-Days",
      portalOwnerPrice: 3850,
      resellerPrice: 3855,
      agentPrice: 3860,
      freeUserPrice: 3865,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "10000",
      planName: "MTN SME Data 10GB",
      serviceType: "SME",
      serviceCode: "MTNSME",
      dataSize: "10GB",
      validity: "30-Days",
      portalOwnerPrice: 5025,
      resellerPrice: 5030,
      agentPrice: 5035,
      freeUserPrice: 5040,
      status: "Active",
    },

    // MTN Gifting Data Plans
    {
      network: "mtn",
      planId: "500",
      planName: "MTN Gifting Data 500MB",
      serviceType: "GIFT",
      serviceCode: "MTNGIFT",
      dataSize: "500MB",
      validity: "7-Days",
      portalOwnerPrice: 490,
      resellerPrice: 495,
      agentPrice: 500,
      freeUserPrice: 505,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "2000",
      planName: "MTN Gifting Data 2GB",
      serviceType: "GIFT",
      serviceCode: "MTNGIFT",
      dataSize: "2GB",
      validity: "30-Days",
      portalOwnerPrice: 1465,
      resellerPrice: 1470,
      agentPrice: 1475,
      freeUserPrice: 1480,
      status: "Active",
    },
    {
      network: "mtn",
      planId: "10000",
      planName: "MTN Gifting Data 10GB",
      serviceType: "GIFT",
      serviceCode: "MTNGIFT",
      dataSize: "10GB",
      validity: "30-Days",
      portalOwnerPrice: 4390,
      resellerPrice: 4395,
      agentPrice: 4400,
      freeUserPrice: 4405,
      status: "Active",
    },

    // Airtel SME Data Plans
    {
      network: "airtel",
      planId: "300",
      planName: "Airtel SME Data 300MB",
      serviceType: "SME",
      serviceCode: "AIRTELSME",
      dataSize: "300MB",
      validity: "2-Days",
      portalOwnerPrice: 125,
      resellerPrice: 130,
      agentPrice: 135,
      freeUserPrice: 140,
      status: "Active",
    },
    {
      network: "airtel",
      planId: "1000D",
      planName: "Airtel SME Data 1GB",
      serviceType: "SME",
      serviceCode: "AIRTELSME",
      dataSize: "1GB",
      validity: "1-Day",
      portalOwnerPrice: 358,
      resellerPrice: 363,
      agentPrice: 368,
      freeUserPrice: 373,
      status: "Active",
    },
    {
      network: "airtel",
      planId: "4000",
      planName: "Airtel SME Data 4GB",
      serviceType: "SME",
      serviceCode: "AIRTELSME",
      dataSize: "4GB",
      validity: "30-Days",
      portalOwnerPrice: 2450,
      resellerPrice: 2455,
      agentPrice: 2460,
      freeUserPrice: 2465,
      status: "Active",
    },
    {
      network: "airtel",
      planId: "10000",
      planName: "Airtel SME Data 10GB",
      serviceType: "SME",
      serviceCode: "AIRTELSME",
      dataSize: "10GB",
      validity: "30-Days",
      portalOwnerPrice: 3100,
      resellerPrice: 3105,
      agentPrice: 3110,
      freeUserPrice: 3115,
      status: "Active",
    },

    // Airtel Corporate Data Plans
    {
      network: "airtel",
      planId: "500",
      planName: "Airtel Corporate Data 500MB",
      serviceType: "CORPORATE",
      serviceCode: "AIRTELCG",
      dataSize: "500MB",
      validity: "30-Days",
      portalOwnerPrice: 533,
      resellerPrice: 538,
      agentPrice: 543,
      freeUserPrice: 548,
      status: "Active",
    },
    {
      network: "airtel",
      planId: "1000",
      planName: "Airtel Corporate Data 1GB",
      serviceType: "CORPORATE",
      serviceCode: "AIRTELCG",
      dataSize: "1GB",
      validity: "30-Days",
      portalOwnerPrice: 1070,
      resellerPrice: 1075,
      agentPrice: 1080,
      freeUserPrice: 1085,
      status: "Active",
    },

    // Glo SME Data Plans
    {
      network: "glo",
      planId: "350",
      planName: "GLO SME Data 350MB",
      serviceType: "SME",
      serviceCode: "GLOSME",
      dataSize: "350MB",
      validity: "1-Day",
      portalOwnerPrice: 100,
      resellerPrice: 105,
      agentPrice: 110,
      freeUserPrice: 115,
      status: "Active",
    },
    {
      network: "glo",
      planId: "750",
      planName: "GLO SME Data 750MB",
      serviceType: "SME",
      serviceCode: "GLOSME",
      dataSize: "750MB",
      validity: "1-Day",
      portalOwnerPrice: 205,
      resellerPrice: 210,
      agentPrice: 215,
      freeUserPrice: 220,
      status: "Active",
    },

    // Glo Corporate Data Plans
    {
      network: "glo",
      planId: "500",
      planName: "GLO Corporate Data 500MB",
      serviceType: "CORPORATE",
      serviceCode: "GLOCG",
      dataSize: "500MB",
      validity: "30-Days",
      portalOwnerPrice: 220,
      resellerPrice: 225,
      agentPrice: 230,
      freeUserPrice: 235,
      status: "Active",
    },
    {
      network: "glo",
      planId: "1000",
      planName: "GLO Corporate Data 1GB",
      serviceType: "CORPORATE",
      serviceCode: "GLOCG",
      dataSize: "1GB",
      validity: "30-Days",
      portalOwnerPrice: 440,
      resellerPrice: 445,
      agentPrice: 450,
      freeUserPrice: 455,
      status: "Active",
    },

    // 9Mobile SME Data Plans
    {
      network: "9mobile",
      planId: "500",
      planName: "9MOBILE SME Data 500MB",
      serviceType: "SME",
      serviceCode: "9MOBILESME",
      dataSize: "500MB",
      validity: "30-Days",
      portalOwnerPrice: 135,
      resellerPrice: 140,
      agentPrice: 145,
      freeUserPrice: 150,
      status: "Active",
    },
    {
      network: "9mobile",
      planId: "3500",
      planName: "9MOBILE SME Data 3.5GB",
      serviceType: "SME",
      serviceCode: "9MOBILESME",
      dataSize: "3.5GB",
      validity: "30-Days",
      portalOwnerPrice: 905,
      resellerPrice: 910,
      agentPrice: 915,
      freeUserPrice: 920,
      status: "Active",
    },

    // 9Mobile Corporate Data Plans
    {
      network: "9mobile",
      planId: "500",
      planName: "9MOBILE Corporate Data 500MB",
      serviceType: "CORPORATE",
      serviceCode: "9MOBILECG",
      dataSize: "500MB",
      validity: "30-Days",
      portalOwnerPrice: 147,
      resellerPrice: 152,
      agentPrice: 157,
      freeUserPrice: 162,
      status: "Active",
    },
    {
      network: "9mobile",
      planId: "1000",
      planName: "9MOBILE Corporate Data 1GB",
      serviceType: "CORPORATE",
      serviceCode: "9MOBILECG",
      dataSize: "1GB",
      validity: "30-Days",
      portalOwnerPrice: 285,
      resellerPrice: 290,
      agentPrice: 295,
      freeUserPrice: 300,
      status: "Active",
    },
  ]
}
