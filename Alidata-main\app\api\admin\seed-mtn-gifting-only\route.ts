import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import { logger } from "@/lib/logger"
import DataPricing from "@/models/DataPricing"
import User from "@/models/User"

// MTN Gifting Data Plans based on the provided image
const mtnGiftingPlans = [
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "40",
    planName: "40MB Gifting",
    dataSize: "40MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 52,
      reseller: 57,
      agent: 62,
      freeUser: 67,
    },
    apiPrice: 57,
    sellingPrice: 67,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "75",
    planName: "75MB Gifting",
    dataSize: "75MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 76,
      reseller: 81,
      agent: 86,
      freeUser: 91,
    },
    apiPrice: 81,
    sellingPrice: 91,
    isActive: false, // Disabled in the image
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "100",
    planName: "100MB Gifting",
    dataSize: "100MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 101,
      reseller: 106,
      agent: 111,
      freeUser: 116,
    },
    apiPrice: 106,
    sellingPrice: 116,
    isActive: false, // Disabled in the image
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "110",
    planName: "110MB Gifting",
    dataSize: "110MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 102,
      reseller: 107,
      agent: 112,
      freeUser: 117,
    },
    apiPrice: 107,
    sellingPrice: 117,
    isActive: false, // Disabled in the image
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "230",
    planName: "230MB Gifting",
    dataSize: "230MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 200,
      reseller: 205,
      agent: 210,
      freeUser: 215,
    },
    apiPrice: 205,
    sellingPrice: 215,
    isActive: false, // Disabled in the image
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "500",
    planName: "500MB Gifting",
    dataSize: "500MB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 490,
      reseller: 495,
      agent: 500,
      freeUser: 505,
    },
    apiPrice: 495,
    sellingPrice: 505,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "750",
    planName: "750MB Gifting",
    dataSize: "750MB",
    validity: "3-Days",
    vtuPricing: {
      portalOwner: 440,
      reseller: 445,
      agent: 450,
      freeUser: 455,
    },
    apiPrice: 445,
    sellingPrice: 455,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1000D",
    planName: "1GB Gifting",
    dataSize: "1GB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 490,
      reseller: 495,
      agent: 500,
      freeUser: 505,
    },
    apiPrice: 495,
    sellingPrice: 505,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1500D",
    planName: "1.5GB Gifting",
    dataSize: "1.5GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 590,
      reseller: 595,
      agent: 600,
      freeUser: 605,
    },
    apiPrice: 595,
    sellingPrice: 605,
    isActive: false, // Disabled in the image
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2000D",
    planName: "2GB Gifting",
    dataSize: "2GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 735,
      reseller: 740,
      agent: 745,
      freeUser: 750,
    },
    apiPrice: 740,
    sellingPrice: 750,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2501D",
    planName: "2.5GB Gifting",
    dataSize: "2.5GB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 735,
      reseller: 740,
      agent: 745,
      freeUser: 750,
    },
    apiPrice: 740,
    sellingPrice: 750,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2500D",
    planName: "2.5GB Gifting",
    dataSize: "2.5GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 880,
      reseller: 885,
      agent: 890,
      freeUser: 895,
    },
    apiPrice: 885,
    sellingPrice: 895,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "3200D",
    planName: "3.2GB Gifting",
    dataSize: "3.2GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 980,
      reseller: 985,
      agent: 990,
      freeUser: 995,
    },
    apiPrice: 985,
    sellingPrice: 995,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1000W",
    planName: "1GB Gifting",
    dataSize: "1GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 780,
      reseller: 785,
      agent: 790,
      freeUser: 795,
    },
    apiPrice: 785,
    sellingPrice: 795,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1500W",
    planName: "1.5GB Gifting",
    dataSize: "1.5GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 975,
      reseller: 980,
      agent: 985,
      freeUser: 990,
    },
    apiPrice: 980,
    sellingPrice: 990,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "6000W",
    planName: "6GB Gifting",
    dataSize: "6GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 2415,
      reseller: 2420,
      agent: 2425,
      freeUser: 2430,
    },
    apiPrice: 2420,
    sellingPrice: 2430,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2000",
    planName: "2GB Gifting",
    dataSize: "2GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1465,
      reseller: 1470,
      agent: 1475,
      freeUser: 1480,
    },
    apiPrice: 1470,
    sellingPrice: 1480,
    isActive: true,
  },
]

// Airtel Gifting Data Plans based on the provided image
const airtelGiftingPlans = [
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "75",
    planName: "Airtel 75MB Gifting",
    dataSize: "75MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 79.9,
      reseller: 84.9,
      agent: 89.9,
      freeUser: 94.9,
    },
    apiPrice: 79.9,
    sellingPrice: 94.9,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "100",
    planName: "Airtel 100MB Gifting",
    dataSize: "100MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 105,
      reseller: 110,
      agent: 115,
      freeUser: 120,
    },
    apiPrice: 105,
    sellingPrice: 120,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "200",
    planName: "Airtel 200MB Gifting",
    dataSize: "200MB",
    validity: "3-Days",
    vtuPricing: {
      portalOwner: 206,
      reseller: 211,
      agent: 216,
      freeUser: 221,
    },
    apiPrice: 206,
    sellingPrice: 221,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "300",
    planName: "Airtel 300MB Gifting",
    dataSize: "300MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 306,
      reseller: 311,
      agent: 316,
      freeUser: 321,
    },
    apiPrice: 306,
    sellingPrice: 321,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "500",
    planName: "Airtel 500MB Gifting",
    dataSize: "500MB",
    validity: "3-Days",
    vtuPricing: {
      portalOwner: 496,
      reseller: 501,
      agent: 506,
      freeUser: 511,
    },
    apiPrice: 496,
    sellingPrice: 511,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "1000D",
    planName: "Airtel 1GB Gifting",
    dataSize: "1GB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 495,
      reseller: 500,
      agent: 505,
      freeUser: 510,
    },
    apiPrice: 495,
    sellingPrice: 510,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "750",
    planName: "Airtel 750MB Gifting",
    dataSize: "750MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 299,
      reseller: 304,
      agent: 309,
      freeUser: 314,
    },
    apiPrice: 299,
    sellingPrice: 314,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "1500D",
    planName: "Airtel 1.5GB Gifting",
    dataSize: "1.5GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 595,
      reseller: 600,
      agent: 605,
      freeUser: 610,
    },
    apiPrice: 595,
    sellingPrice: 610,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "3000D",
    planName: "Airtel 3GB Gifting",
    dataSize: "3GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 990,
      reseller: 995,
      agent: 1000,
      freeUser: 1005,
    },
    apiPrice: 990,
    sellingPrice: 1005,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "5000D",
    planName: "Airtel 5GB Gifting",
    dataSize: "5GB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 299,
      reseller: 304,
      agent: 309,
      freeUser: 314,
    },
    apiPrice: 299,
    sellingPrice: 314,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "1000W",
    planName: "Airtel 1GB Gifting",
    dataSize: "1GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 790,
      reseller: 795,
      agent: 800,
      freeUser: 805,
    },
    apiPrice: 790,
    sellingPrice: 805,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "1500W",
    planName: "Airtel 1.5GB Gifting",
    dataSize: "1.5GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 995,
      reseller: 1000,
      agent: 1005,
      freeUser: 1010,
    },
    apiPrice: 995,
    sellingPrice: 1010,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "3500W",
    planName: "Airtel 3.5GB Gifting",
    dataSize: "3.5GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 1485,
      reseller: 1490,
      agent: 1495,
      freeUser: 1500,
    },
    apiPrice: 1485,
    sellingPrice: 1500,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "6000W",
    planName: "Airtel 6GB Gifting",
    dataSize: "6GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 2493,
      reseller: 2498,
      agent: 2503,
      freeUser: 2508,
    },
    apiPrice: 2493,
    sellingPrice: 2508,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "10000W",
    planName: "Airtel 10GB Gifting",
    dataSize: "10GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 3000,
      reseller: 3005,
      agent: 3010,
      freeUser: 3015,
    },
    apiPrice: 3000,
    sellingPrice: 3015,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "18000W",
    planName: "Airtel 18GB Gifting",
    dataSize: "18GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 4975,
      reseller: 4980,
      agent: 4985,
      freeUser: 4990,
    },
    apiPrice: 4975,
    sellingPrice: 4990,
    isActive: false, // Disabled in the image
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "2000",
    planName: "Airtel 2GB Gifting",
    dataSize: "2GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1485,
      reseller: 1490,
      agent: 1495,
      freeUser: 1500,
    },
    apiPrice: 1485,
    sellingPrice: 1500,
    isActive: true,
  },
  {
    network: "airtel",
    serviceType: "GIFTING",
    serviceCode: "AIRTELGIFT",
    planId: "3000",
    planName: "Airtel 3GB Gifting",
    dataSize: "3GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1980,
      reseller: 1985,
      agent: 1990,
      freeUser: 1995,
    },
    apiPrice: 1980,
    sellingPrice: 1995,
    isActive: true,
  },
]

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Connect to database
    await connectDB()
    logger.info("✅ Connected to MongoDB")

    // Check if user is admin
    const user = await User.findOne({ email: session.user.email })

    if (!user || user.role !== "admin") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    logger.info("🚀 Starting MTN and Airtel Gifting data plans seeding...", { userId: user._id })

    // Clear ALL existing data plans
    const deleteResult = await DataPricing.deleteMany({})
    logger.info(`🗑️ Cleared ${deleteResult.deletedCount} existing data plans`)

    // Insert new MTN Gifting plans only
    const insertMtnResult = await DataPricing.insertMany(mtnGiftingPlans)
    logger.info(`📊 Inserted ${insertMtnResult.length} MTN Gifting data plans`)

    // Insert new Airtel Gifting plans only
    const insertAirtelResult = await DataPricing.insertMany(airtelGiftingPlans)
    logger.info(`📊 Inserted ${insertAirtelResult.length} Airtel Gifting data plans`)

    // Create indexes for better performance
    try {
      await DataPricing.collection.createIndex({ network: 1, serviceType: 1 })
      await DataPricing.collection.createIndex({ planId: 1 }, { unique: true })
      await DataPricing.collection.createIndex({ isActive: 1 })
      logger.info("🔍 Created database indexes")
    } catch (indexError) {
      logger.warn("⚠️ Some indexes may already exist", { error: indexError })
    }

    // Calculate summary statistics for MTN plans
    const totalMtnPlans = mtnGiftingPlans.length
    const activeMtnPlans = mtnGiftingPlans.filter((plan) => plan.isActive).length
    const inactiveMtnPlans = totalMtnPlans - activeMtnPlans

    // Calculate summary statistics for Airtel plans
    const totalAirtelPlans = airtelGiftingPlans.length
    const activeAirtelPlans = airtelGiftingPlans.filter((plan) => plan.isActive).length
    const inactiveAirtelPlans = totalAirtelPlans - activeAirtelPlans

    // Calculate price ranges for MTN plans
    const activeMtnPlanPrices = mtnGiftingPlans.filter((plan) => plan.isActive).map((plan) => plan.sellingPrice)

    // Calculate price ranges for Airtel plans
    const activeAirtelPlanPrices = airtelGiftingPlans.filter((plan) => plan.isActive).map((plan) => plan.sellingPrice)

    const summary = {
      mtn: {
        totalPlans: totalMtnPlans,
        activePlans: activeMtnPlans,
        inactivePlans: inactiveMtnPlans,
        priceRange: {
          min: Math.min(...activeMtnPlanPrices),
          max: Math.max(...activeMtnPlanPrices),
        },
      },
      airtel: {
        totalPlans: totalAirtelPlans,
        activePlans: activeAirtelPlans,
        inactivePlans: inactiveAirtelPlans,
        priceRange: {
          min: Math.min(...activeAirtelPlanPrices),
          max: Math.max(...activeAirtelPlanPrices),
        },
      },
    }

    logger.info("📈 SEEDING SUMMARY:", summary)
    logger.info("🎉 MTN and Airtel Gifting data plans seeded successfully!")

    return NextResponse.json({
      success: true,
      message: `Successfully cleared all data plans and seeded ${totalMtnPlans} MTN Gifting plans with ${activeMtnPlans} active and ${inactiveMtnPlans} inactive plans, and ${totalAirtelPlans} Airtel Gifting plans with ${activeAirtelPlans} active and ${inactiveAirtelPlans} inactive plans.`,
      summary,
    })
  } catch (error) {
    logger.error("❌ Error seeding MTN and Airtel Gifting data plans:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to seed MTN and Airtel Gifting data plans",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
