import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import { logger } from "@/lib/logger"
import DataPricing from "@/models/DataPricing"
import User from "@/models/User"

// MTN Data Plans with updated pricing
const mtnDataPlans = [
  // SME Plans (9 plans)
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "500W",
    planName: "500MB SME",
    dataSize: "500MB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 130,
      reseller: 140,
      agent: 150,
      freeUser: 160,
    },
    apiPrice: 140,
    sellingPrice: 160,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "1000",
    planName: "1GB SME",
    dataSize: "1GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 260,
      reseller: 280,
      agent: 300,
      freeUser: 320,
    },
    apiPrice: 280,
    sellingPrice: 320,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "2000",
    planName: "2GB SME",
    dataSize: "2GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 520,
      reseller: 560,
      agent: 600,
      freeUser: 640,
    },
    apiPrice: 560,
    sellingPrice: 640,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "3000",
    planName: "3GB SME",
    dataSize: "3GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 780,
      reseller: 840,
      agent: 900,
      freeUser: 960,
    },
    apiPrice: 840,
    sellingPrice: 960,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "5000",
    planName: "5GB SME",
    dataSize: "5GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1300,
      reseller: 1400,
      agent: 1500,
      freeUser: 1600,
    },
    apiPrice: 1400,
    sellingPrice: 1600,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "10000",
    planName: "10GB SME",
    dataSize: "10GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 2600,
      reseller: 2800,
      agent: 3000,
      freeUser: 3200,
    },
    apiPrice: 2800,
    sellingPrice: 3200,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "15000",
    planName: "15GB SME",
    dataSize: "15GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 3900,
      reseller: 4200,
      agent: 4500,
      freeUser: 4800,
    },
    apiPrice: 4200,
    sellingPrice: 4800,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "20000",
    planName: "20GB SME",
    dataSize: "20GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 5200,
      reseller: 5600,
      agent: 6000,
      freeUser: 6400,
    },
    apiPrice: 5600,
    sellingPrice: 6400,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "SME",
    serviceCode: "MTNSME",
    planId: "40000",
    planName: "40GB SME",
    dataSize: "40GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 10400,
      reseller: 11200,
      agent: 12000,
      freeUser: 12800,
    },
    apiPrice: 11200,
    sellingPrice: 12800,
    isActive: false, // Inactive plan
  },

  // Gifting Plans (26 plans)
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "25W",
    planName: "25MB Gifting",
    dataSize: "25MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 25,
      reseller: 30,
      agent: 35,
      freeUser: 40,
    },
    apiPrice: 30,
    sellingPrice: 40,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "50W",
    planName: "50MB Gifting",
    dataSize: "50MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 50,
      reseller: 55,
      agent: 60,
      freeUser: 65,
    },
    apiPrice: 55,
    sellingPrice: 65,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "150W",
    planName: "150MB Gifting",
    dataSize: "150MB",
    validity: "1-Day",
    vtuPricing: {
      portalOwner: 100,
      reseller: 110,
      agent: 120,
      freeUser: 130,
    },
    apiPrice: 110,
    sellingPrice: 130,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "250W",
    planName: "250MB Gifting",
    dataSize: "250MB",
    validity: "2-Days",
    vtuPricing: {
      portalOwner: 150,
      reseller: 165,
      agent: 180,
      freeUser: 195,
    },
    apiPrice: 165,
    sellingPrice: 195,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "500D",
    planName: "500MB Gifting",
    dataSize: "500MB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 200,
      reseller: 220,
      agent: 240,
      freeUser: 260,
    },
    apiPrice: 220,
    sellingPrice: 260,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1000D",
    planName: "1GB Gifting",
    dataSize: "1GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 400,
      reseller: 440,
      agent: 480,
      freeUser: 520,
    },
    apiPrice: 440,
    sellingPrice: 520,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1500D",
    planName: "1.5GB Gifting",
    dataSize: "1.5GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 600,
      reseller: 660,
      agent: 720,
      freeUser: 780,
    },
    apiPrice: 660,
    sellingPrice: 780,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2000D",
    planName: "2GB Gifting",
    dataSize: "2GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 800,
      reseller: 880,
      agent: 960,
      freeUser: 1040,
    },
    apiPrice: 880,
    sellingPrice: 1040,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "3000D",
    planName: "3GB Gifting",
    dataSize: "3GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 1200,
      reseller: 1320,
      agent: 1440,
      freeUser: 1560,
    },
    apiPrice: 1320,
    sellingPrice: 1560,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "5000D",
    planName: "5GB Gifting",
    dataSize: "5GB",
    validity: "7-Days",
    vtuPricing: {
      portalOwner: 2000,
      reseller: 2200,
      agent: 2400,
      freeUser: 2600,
    },
    apiPrice: 2200,
    sellingPrice: 2600,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "500M",
    planName: "500MB Gifting",
    dataSize: "500MB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 300,
      reseller: 330,
      agent: 360,
      freeUser: 390,
    },
    apiPrice: 330,
    sellingPrice: 390,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1000M",
    planName: "1GB Gifting",
    dataSize: "1GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 600,
      reseller: 660,
      agent: 720,
      freeUser: 780,
    },
    apiPrice: 660,
    sellingPrice: 780,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2000M",
    planName: "2GB Gifting",
    dataSize: "2GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1200,
      reseller: 1320,
      agent: 1440,
      freeUser: 1560,
    },
    apiPrice: 1320,
    sellingPrice: 1560,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "3000M",
    planName: "3GB Gifting",
    dataSize: "3GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 1800,
      reseller: 1980,
      agent: 2160,
      freeUser: 2340,
    },
    apiPrice: 1980,
    sellingPrice: 2340,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "5000M",
    planName: "5GB Gifting",
    dataSize: "5GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 3000,
      reseller: 3300,
      agent: 3600,
      freeUser: 3900,
    },
    apiPrice: 3300,
    sellingPrice: 3900,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "10000M",
    planName: "10GB Gifting",
    dataSize: "10GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 6000,
      reseller: 6600,
      agent: 7200,
      freeUser: 7800,
    },
    apiPrice: 6600,
    sellingPrice: 7800,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "15000M",
    planName: "15GB Gifting",
    dataSize: "15GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 9000,
      reseller: 9900,
      agent: 10800,
      freeUser: 11700,
    },
    apiPrice: 9900,
    sellingPrice: 11700,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "20000M",
    planName: "20GB Gifting",
    dataSize: "20GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 12000,
      reseller: 13200,
      agent: 14400,
      freeUser: 15600,
    },
    apiPrice: 13200,
    sellingPrice: 15600,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "25000M",
    planName: "25GB Gifting",
    dataSize: "25GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 15000,
      reseller: 16500,
      agent: 18000,
      freeUser: 19500,
    },
    apiPrice: 16500,
    sellingPrice: 19500,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "40000M",
    planName: "40GB Gifting",
    dataSize: "40GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 24000,
      reseller: 26400,
      agent: 28800,
      freeUser: 31200,
    },
    apiPrice: 26400,
    sellingPrice: 31200,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "75000M",
    planName: "75GB Gifting",
    dataSize: "75GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 45000,
      reseller: 49500,
      agent: 54000,
      freeUser: 58500,
    },
    apiPrice: 49500,
    sellingPrice: 58500,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "110000M",
    planName: "110GB Gifting",
    dataSize: "110GB",
    validity: "30-Days",
    vtuPricing: {
      portalOwner: 66000,
      reseller: 72600,
      agent: 79200,
      freeUser: 85800,
    },
    apiPrice: 72600,
    sellingPrice: 85800,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "1000Y",
    planName: "1GB Gifting",
    dataSize: "1GB",
    validity: "365-Days",
    vtuPricing: {
      portalOwner: 3600,
      reseller: 3960,
      agent: 4320,
      freeUser: 4680,
    },
    apiPrice: 3960,
    sellingPrice: 4680,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "2000Y",
    planName: "2GB Gifting",
    dataSize: "2GB",
    validity: "365-Days",
    vtuPricing: {
      portalOwner: 7200,
      reseller: 7920,
      agent: 8640,
      freeUser: 9360,
    },
    apiPrice: 7920,
    sellingPrice: 9360,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "3000Y",
    planName: "3GB Gifting",
    dataSize: "3GB",
    validity: "365-Days",
    vtuPricing: {
      portalOwner: 10800,
      reseller: 11880,
      agent: 12960,
      freeUser: 14040,
    },
    apiPrice: 11880,
    sellingPrice: 14040,
    isActive: true,
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "5000Y",
    planName: "5GB Gifting",
    dataSize: "5GB",
    validity: "365-Days",
    vtuPricing: {
      portalOwner: 18000,
      reseller: 19800,
      agent: 21600,
      freeUser: 23400,
    },
    apiPrice: 19800,
    sellingPrice: 23400,
    isActive: false, // Inactive plan
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    serviceCode: "MTNGIFT",
    planId: "10000Y",
    planName: "10GB Gifting",
    dataSize: "10GB",
    validity: "365-Days",
    vtuPricing: {
      portalOwner: 36000,
      reseller: 39600,
      agent: 43200,
      freeUser: 46800,
    },
    apiPrice: 39600,
    sellingPrice: 46800,
    isActive: false, // Inactive plan
  },
]

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Connect to database
    await connectDB()
    logger.info("✅ Connected to MongoDB")

    // Check if user is admin
    const user = await User.findOne({ email: session.user.email })

    if (!user || user.role !== "admin") {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    logger.info("🚀 Starting MTN data plans seeding...", { userId: user._id })

    // Clear existing MTN plans
    const deleteResult = await DataPricing.deleteMany({ network: "mtn" })
    logger.info(`🗑️ Cleared ${deleteResult.deletedCount} existing MTN data plans`)

    // Insert new MTN plans
    const insertResult = await DataPricing.insertMany(mtnDataPlans)
    logger.info(`📊 Inserted ${insertResult.length} MTN data plans`)

    // Create indexes for better performance
    try {
      await DataPricing.collection.createIndex({ network: 1, serviceType: 1 })
      await DataPricing.collection.createIndex({ planId: 1 }, { unique: true })
      await DataPricing.collection.createIndex({ isActive: 1 })
      logger.info("🔍 Created database indexes")
    } catch (indexError) {
      logger.warn("⚠️ Some indexes may already exist", { error: indexError })
    }

    // Calculate summary statistics
    const totalPlans = mtnDataPlans.length
    const activePlans = mtnDataPlans.filter((plan) => plan.isActive).length
    const inactivePlans = totalPlans - activePlans
    const smePlans = mtnDataPlans.filter((plan) => plan.serviceType === "SME").length
    const giftingPlans = mtnDataPlans.filter((plan) => plan.serviceType === "GIFT").length

    // Calculate price ranges
    const smeActivePlans = mtnDataPlans.filter((plan) => plan.serviceType === "SME" && plan.isActive)
    const giftingActivePlans = mtnDataPlans.filter((plan) => plan.serviceType === "GIFT" && plan.isActive)

    const smePrices = smeActivePlans.map((plan) => plan.sellingPrice)
    const giftingPrices = giftingActivePlans.map((plan) => plan.sellingPrice)

    const summary = {
      totalPlans,
      activePlans,
      inactivePlans,
      smePlans,
      giftingPlans,
      priceRange: {
        sme: {
          min: Math.min(...smePrices),
          max: Math.max(...smePrices),
        },
        gifting: {
          min: Math.min(...giftingPrices),
          max: Math.max(...giftingPrices),
        },
      },
    }

    logger.info("📈 SEEDING SUMMARY:", summary)
    logger.info("🎉 MTN data plans seeded successfully!")

    return NextResponse.json({
      success: true,
      message: `Successfully seeded ${totalPlans} MTN data plans with ${activePlans} active and ${inactivePlans} inactive plans.`,
      summary,
    })
  } catch (error) {
    logger.error("❌ Error seeding MTN data plans:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to seed MTN data plans",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
