import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import bcrypt from "bcryptjs"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"
import DataPricing from "@/models/DataPricing"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Only allow if user is admin or if no users exist yet
    const userCount = await User.countDocuments()
    if (userCount > 0 && (!session?.user?.id || session.user.role !== "admin")) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    // Clear existing data
    await User.deleteMany({})
    await Transaction.deleteMany({})
    await DataPricing.deleteMany({})

    // Create only the admin user
    const adminUser = await User.create({
      firstName: "Aliyu",
      lastName: "Admin",
      email: "aliyu1234",
      phone: "08000000000",
      password: await bcrypt.hash("aliyu1234aliyu", 12),
      balance: 0,
      role: "admin",
    })

    return NextResponse.json({
      message: "Admin user created successfully!",
      data: {
        users: 1,
        dataPricing: 0,
        transactions: 0,
      },
      credentials: {
        admin: { email: "aliyu1234", password: "aliyu1234aliyu" },
      },
    })
  } catch (error) {
    console.error("Seeding error:", error)
    return NextResponse.json({ message: "Seeding failed", error: error.message }, { status: 500 })
  }
}
