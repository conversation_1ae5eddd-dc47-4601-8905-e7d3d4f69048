import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { logger } from "@/lib/logger"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const apiKey = process.env.VTU_API_KEY

    if (!apiKey) {
      return NextResponse.json(
        {
          success: false,
          message: "VTU API key not configured",
        },
        { status: 400 },
      )
    }

    logger.info("Testing VTU Africa API connection", {
      adminId: session.user.id,
    })

    // Test with VTU Africa API
    const testResponse = await testVTUAfricaConnection(apiKey)

    return NextResponse.json(testResponse)
  } catch (error) {
    logger.error("VTU Africa API test error", error)
    return NextResponse.json(
      {
        success: false,
        message: "Test failed",
        error: error.message,
      },
      { status: 500 },
    )
  }
}

async function testVTUAfricaConnection(apiKey: string) {
  try {
    // Use sandbox endpoint for testing
    const baseUrl = "https://vtuafrica.com.ng/portal/api-test/data/"

    logger.info("Making test request to VTU Africa API", {
      baseUrl,
      testParams: {
        service: "MTNSME",
        DataPlan: "500W",
        MobileNumber: "08000000000",
      },
    })

    // Build test URL with query parameters
    const apiUrl = new URL(baseUrl)
    apiUrl.searchParams.append("apikey", apiKey)
    apiUrl.searchParams.append("service", "MTNSME")
    apiUrl.searchParams.append("MobileNumber", "08000000000") // Test number
    apiUrl.searchParams.append("DataPlan", "500W")
    apiUrl.searchParams.append("ref", `TEST_${Date.now()}`)
    apiUrl.searchParams.append("maxamount", "500")

    const response = await fetch(apiUrl.toString(), {
      method: "GET",
      headers: {
        "User-Agent": "Alidata-VTU-App/1.0",
        Accept: "application/json, text/plain, */*",
      },
    })

    const responseText = await response.text()

    logger.info("VTU Africa API test response", {
      status: response.status,
      statusText: response.statusText,
      responseText,
    })

    let responseData: any = { raw: responseText }

    try {
      responseData = JSON.parse(responseText)
    } catch {
      responseData = {
        raw: responseText,
        message: responseText,
      }
    }

    // Check if API is responding (even if test transaction fails due to test number)
    const isApiResponding = response.status === 200 || response.status === 400 || response.status === 422

    if (isApiResponding) {
      return {
        success: true,
        message: "VTU Africa API is responding correctly",
        apiStatus: "Connected",
        response: {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        },
        note: "This was a test connection with dummy data. Real transactions will work with valid parameters.",
        endpoint: "VTU Africa Official API",
        documentation: "https://vtuafrica.com.ng/api/data.php",
      }
    } else {
      return {
        success: false,
        message: "VTU Africa API connection failed",
        apiStatus: "Disconnected",
        response: {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        },
      }
    }
  } catch (error) {
    logger.error("VTU Africa API test connection failed", error)

    return {
      success: false,
      message: "Network error connecting to VTU Africa API",
      apiStatus: "Error",
      error: error.message,
    }
  }
}
