import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    const user = await User.findById(params.id).select("-password")
    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 })
    }

    const transactions = await Transaction.find({ userId: params.id }).sort({ createdAt: -1 }).limit(20)

    const transactionStats = await Transaction.aggregate([
      { $match: { userId: user._id } },
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ])

    return NextResponse.json({
      user,
      transactions,
      transactionStats,
    })
  } catch (error) {
    console.error("Admin user detail error:", error)
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    const { balance } = await request.json()

    const user = await User.findByIdAndUpdate(params.id, { balance }, { new: true }).select("-password")

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 })
    }

    // Create transaction record for balance adjustment
    await Transaction.create({
      userId: params.id,
      type: "funding",
      amount: balance,
      status: "completed",
      description: `Balance adjustment by admin`,
      reference: `ADJ_${Date.now()}`,
    })

    return NextResponse.json({ user })
  } catch (error) {
    console.error("Admin user update error:", error)
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}
