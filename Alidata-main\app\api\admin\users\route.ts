import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import { logger } from "@/lib/logger"

export async function GET(request: NextRequest) {
  const startTime = Date.now()

  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      logger.warn("Unauthorized admin users access attempt", {
        userId: session?.user?.id,
        userRole: session?.user?.role,
        hasSession: !!session,
        ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
      })
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()
    logger.debug("Database connection established for admin users query", {
      adminId: session.user.id,
    })

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""

    logger.info("Admin users query started", {
      adminId: session.user.id,
      page,
      limit,
      search,
    })

    const skip = (page - 1) * limit

    // Build search query
    const searchQuery = search
      ? {
          $or: [
            { firstName: { $regex: search, $options: "i" } },
            { lastName: { $regex: search, $options: "i" } },
            { email: { $regex: search, $options: "i" } },
            { phone: { $regex: search, $options: "i" } },
          ],
        }
      : {}

    const users = await User.find({ ...searchQuery, role: "user" })
      .select("-password")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)

    const totalUsers = await User.countDocuments({ ...searchQuery, role: "user" })

    // Get user statistics
    const stats = await User.aggregate([
      { $match: { role: "user" } },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          totalBalance: { $sum: "$balance" },
          avgBalance: { $avg: "$balance" },
        },
      },
    ])

    logger.info("Admin users query successful", {
      adminId: session.user.id,
      usersReturned: users.length,
      totalUsers,
      page,
      search,
      processingTime: Date.now() - startTime,
    })

    return NextResponse.json({
      users,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalUsers / limit),
        totalUsers,
        hasNext: page * limit < totalUsers,
        hasPrev: page > 1,
      },
      stats: stats[0] || { totalUsers: 0, totalBalance: 0, avgBalance: 0 },
    })
  } catch (error) {
    logger.error("Admin users query error", error, {
      processingTime: Date.now() - startTime,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
    })
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}
