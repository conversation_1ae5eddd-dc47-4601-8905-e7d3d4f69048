import NextAuth from "next-auth"
import type { NextAuthOptions } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import { logger } from "@/lib/logger"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          logger.warn("Login attempt with missing credentials")
          return null
        }

        try {
          await connectDB()

          const user = await User.findOne({ email: credentials.email })
          if (!user) {
            logger.warn("Login attempt with non-existent email", {
              email: credentials.email,
            })
            return null
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
          if (!isPasswordValid) {
            logger.warn("Login attempt with invalid password", {
              email: credentials.email,
            })
            return null
          }

          logger.info("User login successful", {
            userId: user._id,
            email: user.email,
          })

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name, // Ensure name is returned here
            role: user.role,
            balance: user.balance || user.walletBalance || 0,
            walletBalance: user.balance || user.walletBalance || 0,
          }
        } catch (error) {
          logger.error("Login error", error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.id = user.id
        token.role = user.role
        token.balance = user.balance || user.walletBalance || 0
        token.walletBalance = user.walletBalance || user.balance || 0 // Ensure walletBalance is also set
        token.name = user.name // Add user's name to the token
      }

      // Update token with fresh data from database when session is updated
      if (trigger === "update" && token.id) {
        try {
          await connectDB()
          const dbUser = await User.findById(token.id)
          if (dbUser) {
            const currentBalance = dbUser.balance || dbUser.walletBalance || 0
            token.balance = currentBalance
            token.walletBalance = currentBalance
            token.name = dbUser.name // Update name in token as well

            logger.info("Token updated with fresh balance and name", {
              userId: token.id,
              newBalance: currentBalance,
              newName: dbUser.name,
            })
          }
        } catch (error) {
          logger.error("Error updating token with fresh data", error)
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.balance = token.balance as number
        session.user.walletBalance = token.walletBalance as number
        session.user.name = token.name as string // Add user's name to the session
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/login",
    signUp: "/auth/register",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
}

const handler = NextAuth(authOptions)
export { handler as GET, handler as POST }
