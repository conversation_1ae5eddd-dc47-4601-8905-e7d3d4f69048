import { type NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import { logger } from "@/lib/logger"

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let requestData: any = {}

  try {
    await connectDB()
    logger.info("Database connection established for user registration")

    requestData = await request.json()
    const { firstName, lastName, email, phone, password } = requestData // 'phone' is expected here

    logger.info("Registration attempt", {
      email,
      firstName,
      lastName,
      phone,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
      userAgent: request.headers.get("user-agent"),
    })

    // Validate required fields
    if (!firstName || !lastName || !email || !phone || !password) {
      logger.warn("Registration failed - missing required fields", {
        email,
        missingFields: {
          firstName: !firstName,
          lastName: !lastName,
          email: !email,
          phone: !phone,
          password: !password,
        },
      })
      return NextResponse.json({ message: "All fields are required" }, { status: 400 })
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email })
    if (existingUser) {
      logger.warn("Registration failed - user already exists", {
        email,
        existingUserId: existingUser._id,
      })
      return NextResponse.json({ message: "User already exists" }, { status: 400 })
    }

    // Hash password
    logger.debug("Hashing password for new user", { email })
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const newUser = await User.create({
      firstName,
      lastName,
      email,
      phone, // This is where 'phone' is used
      password: hashedPassword,
      balance: 0,
    })

    logger.info("User registration successful", {
      userId: newUser._id,
      email,
      firstName,
      lastName,
      phone,
      processingTime: Date.now() - startTime,
    })

    return NextResponse.json({ message: "User created successfully" }, { status: 201 })
  } catch (error) {
    logger.error("User registration failed", error, {
      requestData: {
        ...requestData,
        password: "[REDACTED]", // Never log passwords
      },
      processingTime: Date.now() - startTime,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
      userAgent: request.headers.get("user-agent"),
    })

    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}
