import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"
import { logger } from "@/lib/logger"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      logger.warn("Dashboard stats request without authentication")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await connectDB()

    // Get user details
    const user = await User.findById(session.user.id)
    if (!user) {
      logger.error("Dashboard stats - user not found", null, {
        userId: session.user.id,
      })
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get user's transactions for statistics
    const transactions = await Transaction.find({ userId: session.user.id })

    // Calculate statistics
    const completedTransactions = transactions.filter((t) => t.status === "completed")
    const totalSpent = completedTransactions.reduce((sum, t) => sum + (t.amount || 0), 0)
    const totalTransactions = completedTransactions.length

    // Get current balance - handle both field names for compatibility
    const currentBalance = user.balance || user.walletBalance || 0

    const stats = {
      balance: currentBalance,
      walletBalance: currentBalance, // For backward compatibility
      totalSpent,
      totalTransactions,
      recentTransactions: transactions
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5)
        .map((t) => ({
          id: t._id,
          type: t.type,
          amount: t.amount,
          status: t.status,
          description: t.description,
          createdAt: t.createdAt,
          network: t.network,
          phoneNumber: t.phoneNumber,
        })),
    }

    logger.info("Dashboard stats retrieved successfully", {
      userId: session.user.id,
      balance: currentBalance,
      totalSpent,
      totalTransactions,
    })

    return NextResponse.json(stats)
  } catch (error) {
    logger.error("Dashboard stats error", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
