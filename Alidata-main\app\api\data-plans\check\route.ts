import { NextResponse } from "next/server"
import { connectDB } from "@/lib/mongodb"
import DataPricing from "@/models/DataPricing"
import { logger } from "@/lib/logger"

export async function GET() {
  try {
    await connectDB()

    const totalPlans = await DataPricing.countDocuments()
    const activePlans = await DataPricing.countDocuments({ isActive: true })
    const networks = await DataPricing.distinct("network")
    const serviceTypes = await DataPricing.distinct("serviceType")

    const samplePlans = await DataPricing.find().limit(5).lean()

    logger.info("Data plans check completed", {
      totalPlans,
      activePlans,
      networks,
      serviceTypes,
    })

    return NextResponse.json({
      success: true,
      stats: {
        totalPlans,
        activePlans,
        networks,
        serviceTypes,
      },
      samplePlans: samplePlans.map((plan) => ({
        network: plan.network,
        serviceType: plan.serviceType,
        planName: plan.planName,
        dataSize: plan.dataSize,
        sellingPrice: plan.sellingPrice,
        isActive: plan.isActive,
      })),
    })
  } catch (error) {
    logger.error("Error checking data plans", { error })
    return NextResponse.json(
      {
        success: false,
        error: "Failed to check data plans",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
