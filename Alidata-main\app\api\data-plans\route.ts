import { type NextRequest, NextResponse } from "next/server"
import { connectDB } from "@/lib/mongodb"
import DataPricing from "@/models/DataPricing"

export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const network = searchParams.get("network")
    const serviceType = searchParams.get("serviceType")

    const query: any = { isActive: true }
    if (network && network !== "all") {
      query.network = network.toLowerCase()
    }
    if (serviceType && serviceType !== "all") {
      query.serviceType = serviceType.toUpperCase()
    }

    const plans = await DataPricing.find(query).sort({ network: 1, dataSize: 1, sellingPrice: 1 })

    return NextResponse.json({ success: true, message: "Data plans fetched successfully", data: plans })
  } catch (error) {
    console.error("Error fetching data plans:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch data plans" }, { status: 500 })
  }
}
