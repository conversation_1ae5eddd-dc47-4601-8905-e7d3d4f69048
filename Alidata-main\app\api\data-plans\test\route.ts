import { NextResponse } from "next/server"
import { connectDB } from "@/lib/mongodb"
import DataPricing from "@/models/DataPricing"
import { logger } from "@/lib/logger"

export async function GET() {
  try {
    await connectDB()

    // Get first 5 plans for testing
    const samplePlans = await DataPricing.find({ isActive: true }).limit(5).lean()

    logger.info("Sample plans fetched", { count: samplePlans.length })

    return NextResponse.json({
      success: true,
      data: samplePlans,
      count: samplePlans.length,
    })
  } catch (error) {
    logger.error("Error fetching sample plans", { error })
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch sample plans",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
