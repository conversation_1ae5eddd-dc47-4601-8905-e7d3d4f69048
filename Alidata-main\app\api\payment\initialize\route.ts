import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectToDatabase } from "@/lib/mongodb"
import Transaction from "@/models/Transaction"
import User from "@/models/User"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    const { amount } = await request.json()

    if (!amount || amount < 100) {
      return NextResponse.json({ success: false, message: "Invalid amount. Minimum is ₦100" }, { status: 400 })
    }

    if (amount > 500000) {
      return NextResponse.json({ success: false, message: "Maximum funding amount is ₦500,000" }, { status: 400 })
    }

    await connectToDatabase()

    // Get user details
    const user = await User.findById(session.user.id)
    if (!user) {
      return NextResponse.json({ success: false, message: "User not found" }, { status: 404 })
    }

    // Create transaction record
    const transaction = new Transaction({
      userId: session.user.id,
      type: "funding",
      amount: amount,
      status: "pending",
      description: `Wallet funding of ₦${amount.toLocaleString()}`,
      reference: `fund_${Date.now()}_${session.user.id}`,
      metadata: {
        email: user.email,
        name: user.name,
        fundingAmount: amount,
      },
    })

    await transaction.save()

    // Initialize Paystack payment
    const paystackResponse = await fetch("https://api.paystack.co/transaction/initialize", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: user.email,
        amount: amount * 100, // Paystack expects amount in kobo
        reference: transaction.reference,
        callback_url: `${process.env.NEXTAUTH_URL}/dashboard/payment-callback`,
        metadata: {
          userId: session.user.id,
          transactionId: transaction._id.toString(),
          type: "funding",
        },
      }),
    })

    const paystackData = await paystackResponse.json()

    if (!paystackData.status) {
      // Update transaction status to failed
      await Transaction.findByIdAndUpdate(transaction._id, {
        status: "failed",
        description: `Wallet funding failed: ${paystackData.message}`,
      })

      return NextResponse.json(
        {
          success: false,
          message: "Payment initialization failed",
          error: paystackData.message,
        },
        { status: 400 },
      )
    }

    // Update transaction with Paystack reference
    await Transaction.findByIdAndUpdate(transaction._id, {
      paystackReference: paystackData.data.reference,
    })

    return NextResponse.json({
      success: true,
      message: "Payment initialized successfully",
      data: paystackData.data,
    })
  } catch (error) {
    console.error("Payment initialization error:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
