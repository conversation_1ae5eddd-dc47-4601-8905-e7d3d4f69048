import { type NextRequest, NextResponse } from "next/server"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"
import { logger } from "@/lib/logger"

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let requestData: any = {}

  try {
    await connectDB()
    logger.debug("Database connection established for payment verification")

    requestData = await request.json()
    const { reference } = requestData

    if (!reference) {
      logger.warn("Payment verification attempt without reference", {
        ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
      })
      return NextResponse.json({ message: "Reference is required" }, { status: 400 })
    }

    logger.info("Payment verification started", {
      reference,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
    })

    // Verify payment with Paystack
    logger.debug("Verifying payment with Paystack", { reference })

    const paystackResponse = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
      },
    })

    const paystackData = await paystackResponse.json()

    logger.debug("Paystack verification response", {
      reference,
      status: paystackData.status,
      dataStatus: paystackData.data?.status,
    })

    if (paystackData.status && paystackData.data.status === "success") {
      const amount = paystackData.data.amount / 100 // Convert from kobo to naira
      const userId = reference.split("_")[2]

      logger.info("Payment verified successfully", {
        reference,
        amount,
        userId,
        paystackAmount: paystackData.data.amount,
      })

      // Update user balance
      const user = await User.findById(userId)
      if (user) {
        const previousBalance = user.balance
        user.balance += amount
        await user.save()

        logger.info("User balance updated", {
          userId,
          email: user.email,
          previousBalance,
          newBalance: user.balance,
          amountAdded: amount,
        })

        // Create transaction record
        const transaction = await Transaction.create({
          userId,
          type: "funding",
          amount,
          status: "completed",
          description: "Wallet funding via Paystack",
          reference,
        })

        logger.info("Transaction record created", {
          transactionId: transaction._id,
          userId,
          amount,
          reference,
          processingTime: Date.now() - startTime,
        })

        return NextResponse.json({
          message: "Payment verified successfully",
          amount,
        })
      } else {
        logger.error("Payment verification failed - user not found", null, {
          reference,
          userId,
          amount,
        })
        return NextResponse.json({ message: "User not found" }, { status: 404 })
      }
    } else {
      logger.warn("Payment verification failed", {
        reference,
        paystackStatus: paystackData.status,
        paystackDataStatus: paystackData.data?.status,
        paystackMessage: paystackData.message,
      })
      return NextResponse.json({ message: "Payment verification failed" }, { status: 400 })
    }
  } catch (error) {
    logger.error("Payment verification error", error, {
      requestData,
      processingTime: Date.now() - startTime,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
    })
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}
