import { type NextRequest, NextResponse } from "next/server"
import { spawn } from "child_process"
import path from "path"

export async function POST(request: NextRequest) {
  try {
    const { script } = await request.json()

    if (!script || script !== "seed-mtn-gifting-only.js") {
      return NextResponse.json({ error: "Invalid script name" }, { status: 400 })
    }

    const scriptPath = path.join(process.cwd(), "scripts", script)

    // Create a readable stream for the response
    const stream = new ReadableStream({
      start(controller) {
        const child = spawn("node", [scriptPath], {
          stdio: ["pipe", "pipe", "pipe"],
          env: { ...process.env },
        })

        child.stdout.on("data", (data) => {
          controller.enqueue(new TextEncoder().encode(data.toString()))
        })

        child.stderr.on("data", (data) => {
          controller.enqueue(new TextEncoder().encode(`ERROR: ${data.toString()}`))
        })

        child.on("close", (code) => {
          controller.enqueue(new TextEncoder().encode(`\nScript finished with exit code: ${code}\n`))
          controller.close()
        })

        child.on("error", (error) => {
          controller.enqueue(new TextEncoder().encode(`\nScript error: ${error.message}\n`))
          controller.close()
        })
      },
    })

    return new Response(stream, {
      headers: {
        "Content-Type": "text/plain",
        "Transfer-Encoding": "chunked",
      },
    })
  } catch (error) {
    return NextResponse.json({ error: error instanceof Error ? error.message : "Unknown error" }, { status: 500 })
  }
}
