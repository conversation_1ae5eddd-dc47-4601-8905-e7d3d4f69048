import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"
import { logger } from "@/lib/logger"

export async function GET(request: NextRequest) {
  const startTime = Date.now()

  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      logger.warn("Profile access attempt without authentication", {
        ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
        userAgent: request.headers.get("user-agent"),
        hasSession: !!session,
      })
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    await connectDB()
    logger.debug("Database connection established for profile query", {
      userId: session.user.id,
    })

    const user = await User.findById(session.user.id).select("-password")
    if (!user) {
      logger.error("Profile query failed - user not found", null, {
        userId: session.user.id,
        sessionEmail: session.user.email,
      })
      return NextResponse.json({ message: "User not found" }, { status: 404 })
    }

    const transactions = await Transaction.find({ userId: session.user.id }).sort({ createdAt: -1 }).limit(10)

    // Ensure balance is properly returned
    const userProfile = {
      ...user.toObject(),
      balance: user.balance || user.walletBalance || 0,
      walletBalance: user.balance || user.walletBalance || 0, // For backward compatibility
    }

    logger.info("Profile data retrieved successfully", {
      userId: session.user.id,
      email: user.email,
      balance: userProfile.balance,
      transactionCount: transactions.length,
      processingTime: Date.now() - startTime,
    })

    return NextResponse.json({
      user: userProfile,
      transactions,
    })
  } catch (error) {
    logger.error("Profile query error", error, {
      processingTime: Date.now() - startTime,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
    })
    return NextResponse.json({ message: "Internal server error" }, { status: 500 })
  }
}
