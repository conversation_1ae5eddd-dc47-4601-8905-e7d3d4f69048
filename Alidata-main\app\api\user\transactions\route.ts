import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import Transaction from "@/models/Transaction"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: "Unauthorized access",
          error: "Please log in to view transactions",
          transactions: [],
        },
        { status: 401 },
      )
    }

    await connectDB()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const type = searchParams.get("type")
    const status = searchParams.get("status")

    // Build filter
    const filter: any = { userId: session.user.id }
    if (type && type !== "all") {
      filter.type = type
    }
    if (status && status !== "all") {
      filter.status = status
    }

    // Calculate skip
    const skip = (page - 1) * limit

    // Get transactions with pagination
    const transactions = await Transaction.find(filter).sort({ createdAt: -1 }).skip(skip).limit(limit).lean()

    // Get total count for pagination
    const totalTransactions = await Transaction.countDocuments(filter)
    const totalPages = Math.ceil(totalTransactions / limit)

    // Format transactions for frontend
    const formattedTransactions = transactions.map((transaction) => ({
      _id: transaction._id.toString(),
      type: transaction.type,
      amount: transaction.amount,
      status: transaction.status,
      description: transaction.description,
      reference: transaction.reference,
      network: transaction.network || null,
      phoneNumber: transaction.phoneNumber || null,
      createdAt: transaction.createdAt,
      completedAt: transaction.completedAt || null,
      failureReason: transaction.failureReason || null,
      metadata: transaction.metadata || {},
    }))

    return NextResponse.json({
      success: true,
      message: "Transactions retrieved successfully",
      data: {
        transactions: formattedTransactions,
        pagination: {
          currentPage: page,
          totalPages,
          totalTransactions,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    })
  } catch (error) {
    console.error("Error fetching transactions:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch transactions",
        error: "Internal server error",
        transactions: [],
      },
      { status: 500 },
    )
  }
}
