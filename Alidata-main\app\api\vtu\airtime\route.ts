import { NextResponse } from "next/server"
import { connectDB } from "@/lib/mongodb"
import Transaction from "@/models/Transaction"
import User from "@/models/User"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { logger } from "@/lib/logger"

export async function POST(req: Request) {
  const session = await getServerSession(authOptions)

  if (!session) {
    return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
  }

  await connectDB()

  const { phoneNumber, network, amount } = await req.json()

  if (!phoneNumber || !network || !amount) {
    return NextResponse.json({ success: false, message: "Missing required fields" }, { status: 400 })
  }

  try {
    const user = await User.findById(session.user.id)

    if (!user) {
      return NextResponse.json({ success: false, message: "User not found" }, { status: 404 })
    }

    if (user.balance < amount) {
      return NextResponse.json({ success: false, message: "Insufficient balance" }, { status: 400 })
    }

    // Record transaction
    const transaction = await Transaction.create({
      userId: user._id,
      type: "airtime",
      amount: amount,
      status: "pending",
      description: `Airtime purchase for ${phoneNumber} (${network})`,
      details: {
        phoneNumber,
        network,
        amount,
      },
    })

    const vtuApiUrl = "https://vtuafrica.com.ng/portal/api/airtime/"
    const vtuApiKey = process.env.VTU_API_KEY

    if (!vtuApiUrl || !vtuApiKey) {
      logger.error("VTU API URL or Key not configured for airtime purchase")
      return NextResponse.json(
        { success: false, message: "VTU service not configured", error: "Server configuration error" },
        { status: 500 },
      )
    }

    const queryParams = new URLSearchParams({
      apikey: vtuApiKey,
      network: network,
      phone: phoneNumber,
      amount: amount.toString(), // Ensure amount is a string for URL parameter
      ref: transaction._id.toString(), // Use the generated transaction reference
    }).toString()

    const fullVtuUrl = `${vtuApiUrl}?${queryParams}`

    logger.info("Calling VTU Airtime API", {
      userId: user._id,
      transactionId: transaction._id,
      fullVtuUrl: fullVtuUrl.replace(vtuApiKey, "********"), // Mask API key for logs
    })

    const vtuResponse = await fetch(fullVtuUrl, {
      method: "GET", // Changed to GET
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!vtuResponse.ok) {
      const errorText = await vtuResponse.text()
      logger.error("VTU Airtime API call failed with non-OK status", null, {
        userId: user._id,
        transactionId: transaction._id,
        statusCode: vtuResponse.status,
        statusText: vtuResponse.statusText,
        responseText: errorText,
      })
      throw new Error(`VTU API error: ${vtuResponse.status} ${vtuResponse.statusText} - ${errorText}`)
    }

    const vtuResult = await vtuResponse.json()
    logger.info("VTU Airtime API response received", {
      userId: user._id,
      transactionId: transaction._id,
      vtuResult,
    })

    // --- START OF CHANGE ---
    // Check for success based on vtuafrica.com.ng API response structure
    // Success is indicated by vtuResult.description.Status === "Completed"
    if (vtuResult.description?.Status === "Completed") {
      // --- END OF CHANGE ---
      // Deduct amount from user's balance
      user.balance -= amount
      await user.save()

      // Update transaction status
      transaction.status = "completed"
      transaction.vtuResponse = vtuResult // Store VTU API response for debugging/auditing
      await transaction.save()

      logger.info("Airtime purchase successful", {
        userId: user._id,
        phoneNumber,
        network,
        amount,
        newBalance: user.balance,
        transactionId: transaction._id,
      })

      return NextResponse.json({
        success: true,
        message: "Airtime purchase successful",
        data: {
          newBalance: user.balance,
          transactionId: transaction._id,
          vtuResponse: vtuResult,
        },
      })
    } else {
      // VTU API indicated failure or an unexpected status
      transaction.status = "failed"
      transaction.error =
        vtuResult.message || vtuResult.description?.message || "VTU API reported failure or unexpected status"
      transaction.vtuResponse = vtuResult
      await transaction.save()

      logger.warn("VTU Airtime API reported failure or unexpected status", {
        userId: user._id,
        transactionId: transaction._id,
        vtuResult,
      })

      return NextResponse.json(
        {
          success: false,
          error: vtuResult.message || vtuResult.description?.message || "Airtime purchase failed by VTU service",
          message: "Airtime purchase failed",
        },
        { status: 400 },
      )
    }
  } catch (error) {
    logger.error("Airtime purchase failed", { error })
    return NextResponse.json(
      {
        success: false,
        error: "Airtime purchase failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
