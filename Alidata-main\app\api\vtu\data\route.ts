import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { connectDB } from "@/lib/mongodb"
import User from "@/models/User"
import Transaction from "@/models/Transaction"
import DataPricing from "@/models/DataPricing"
import { logger } from "@/lib/logger"

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      logger.warn("Data purchase attempt without authentication")
      return NextResponse.json(
        { success: false, message: "Unauthorized", error: "Please login to continue" },
        { status: 401 },
      )
    }

    const { network, planId, phoneNumber, amount } = await request.json()

    // Validate input
    if (!network || !planId || !phoneNumber || !amount) {
      logger.warn("Data purchase with missing fields", {
        userId: session.user.id,
        hasNetwork: !!network,
        hasPlanId: !!planId,
        hasPhoneNumber: !!phoneNumber,
        hasAmount: !!amount,
      })
      return NextResponse.json(
        { success: false, message: "Missing required fields", error: "All fields are required" },
        { status: 400 },
      )
    }

    const purchaseAmount = Number.parseFloat(amount)

    if (purchaseAmount <= 0) {
      return NextResponse.json(
        { success: false, message: "Invalid amount", error: "Amount must be greater than 0" },
        { status: 400 },
      )
    }

    await connectDB()

    // Get user and check balance
    const user = await User.findById(session.user.id)
    if (!user) {
      logger.error("Data purchase failed - user not found", null, {
        userId: session.user.id,
      })
      return NextResponse.json(
        { success: false, message: "User not found", error: "User account not found" },
        { status: 404 },
      )
    }

    const currentBalance = user.balance || user.walletBalance || 0

    if (currentBalance < purchaseAmount) {
      logger.warn("Data purchase failed - insufficient balance", {
        userId: session.user.id,
        currentBalance,
        requestedAmount: purchaseAmount,
      })
      return NextResponse.json(
        {
          success: false,
          message: `Insufficient balance. Current balance: ₦${currentBalance.toLocaleString()}`,
          error: "Insufficient funds",
        },
        { status: 400 },
      )
    }

    // Verify data plan exists
    const dataPlan = await DataPricing.findById(planId)
    if (!dataPlan) {
      logger.warn("Data purchase with invalid plan ID", {
        userId: session.user.id,
        planId,
      })
      return NextResponse.json(
        { success: false, message: "Invalid data plan", error: "Selected plan not found" },
        { status: 400 },
      )
    }

    // Create transaction record
    const transaction = new Transaction({
      userId: user._id,
      type: "data",
      network: network.toLowerCase(),
      phoneNumber,
      amount: purchaseAmount,
      planId: planId,
      planDetails: {
        size: dataPlan.size || dataPlan.dataSize,
        validity: dataPlan.validity,
        network: dataPlan.network,
      },
      status: "pending",
      reference: `DATA_${Date.now()}_${user._id}`,
      description: `Data purchase: ${dataPlan.size || dataPlan.dataSize} for ${phoneNumber} (${network})`,
    })

    await transaction.save()

    try {
      logger.info("Processing data purchase", {
        userId: user._id,
        network,
        phoneNumber,
        amount: purchaseAmount,
        planId,
        planSize: dataPlan.size || dataPlan.dataSize,
        transactionId: transaction._id,
      })

      const vtuApiBaseUrl = "https://vtuafrica.com.ng/portal/api/data/"
      const vtuApiKey = process.env.VTU_API_KEY

      if (!vtuApiBaseUrl || !vtuApiKey) {
        logger.error("VTU API URL or Key not configured for data purchase")
        return NextResponse.json(
          { success: false, message: "VTU service not configured", error: "Server configuration error" },
          { status: 500 },
        )
      }

      // Construct query parameters based on user's explicit mapping
      const queryParams = new URLSearchParams({
        apikey: vtuApiKey,
        service: dataPlan.serviceCode, // Use serviceCode from DB for 'service' parameter
        MobileNumber: phoneNumber,
        DataPlan: dataPlan.planId, // Use planId from DB for 'DataPlan' parameter
        ref: transaction.reference, // Use the generated transaction reference
      }).toString()

      const vtuApiUrl = `${vtuApiBaseUrl}?${queryParams}`

      logger.info("Calling VTU Data API", {
        userId: user._id,
        transactionId: transaction._id,
        vtuApiUrl: vtuApiUrl.replace(vtuApiKey, "********"), // Mask API key for logs
      })

      const vtuApiResponse = await fetch(vtuApiUrl, {
        method: "GET", // VTU Africa API uses GET for data purchases
      })

      if (!vtuApiResponse.ok) {
        const errorText = await vtuApiResponse.text()
        logger.error("VTU Data API call failed with non-OK status", null, {
          userId: user._id,
          transactionId: transaction._id,
          statusCode: vtuApiResponse.status,
          statusText: vtuApiResponse.statusText,
          responseText: errorText,
        })
        throw new Error(`VTU API error: ${vtuApiResponse.status} ${vtuApiResponse.statusText} - ${errorText}`)
      }

      const vtuResult = await vtuApiResponse.json()
      logger.info("VTU Data API response received", {
        userId: user._id,
        transactionId: transaction._id,
        vtuResult,
      })

      // Check for success based on VTU Africa API response structure (code: 101 and Status: "Completed")
      if (vtuResult.code === 101 && vtuResult.description?.Status === "Completed") {
        // Deduct amount from user balance
        const newBalance = currentBalance - purchaseAmount
        await User.findByIdAndUpdate(user._id, {
          $set: { balance: newBalance },
          $inc: { totalSpent: purchaseAmount },
        })

        // Update transaction status
        await Transaction.findByIdAndUpdate(transaction._id, {
          status: "completed",
          completedAt: new Date(),
          vtuResponse: vtuResult, // Store VTU API response for debugging/auditing
        })

        logger.info("Data purchase completed successfully", {
          userId: user._id,
          transactionId: transaction._id,
          amount: purchaseAmount,
          newBalance,
          processingTime: Date.now() - startTime,
        })

        return NextResponse.json({
          success: true,
          message: `${dataPlan.size || dataPlan.dataSize} data sent to ${phoneNumber}`,
          data: {
            transactionId: transaction._id,
            reference: transaction.reference,
            newBalance,
            amount: purchaseAmount,
            network,
            phoneNumber,
            planDetails: {
              size: dataPlan.size || dataPlan.dataSize,
              validity: dataPlan.validity,
            },
            vtuResponse: vtuResult,
          },
        })
      } else {
        // VTU API failed or returned an unexpected status
        await Transaction.findByIdAndUpdate(transaction._id, {
          status: "failed",
          failedAt: new Date(),
          vtuResponse: vtuResult,
          error: vtuResult.message || vtuResult.description?.message || "VTU service reported failure",
        })

        logger.error("VTU API failed for data purchase", null, {
          userId: user._id,
          transactionId: transaction._id,
          vtuResult,
        })

        return NextResponse.json(
          {
            success: false,
            message: "Data purchase failed",
            error: vtuResult.message || vtuResult.description?.message || "VTU service temporarily unavailable",
          },
          { status: 500 },
        )
      }
    } catch (vtuError) {
      // Handle VTU API error
      await Transaction.findByIdAndUpdate(transaction._id, {
        status: "failed",
        failedAt: new Date(),
        error: vtuError instanceof Error ? vtuError.message : "VTU API error",
      })

      logger.error("VTU API error during data purchase", vtuError, {
        userId: user._id,
        transactionId: transaction._id,
      })

      return NextResponse.json(
        {
          success: false,
          message: "Data purchase failed",
          error: "Service temporarily unavailable",
        },
        { status: 500 },
      )
    }
  } catch (error) {
    logger.error("Data purchase error", error, {
      processingTime: Date.now() - startTime,
    })
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: "An unexpected error occurred",
      },
      { status: 500 },
    )
  }
}
