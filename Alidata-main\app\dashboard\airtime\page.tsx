"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Smartphone, Zap, CheckCircle } from "lucide-react"
import { toast } from "sonner"

const networks = [
  { id: "mtn", name: "MTN", color: "bg-yellow-500" },
  { id: "airtel", name: "Airtel", color: "bg-red-500" },
  { id: "glo", name: "Glo", color: "bg-green-500" },
  { id: "9mobile", name: "9mobile", color: "bg-green-600" },
]

const quickAmounts = [50, 100, 200, 500, 1000, 2000, 5000]

interface UserStats {
  balance: number
  totalSpent: number
  totalTransactions: number
}

export default function AirtimePage() {
  const { data: session, update } = useSession()
  const [phoneNumber, setPhoneNumber] = useState("")
  const [network, setNetwork] = useState("")
  const [amount, setAmount] = useState<number | string>("")
  const [loading, setLoading] = useState(false)
  const [userStats, setUserStats] = useState<UserStats | null>(null)

  // Fetch user stats
  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch("/api/dashboard/stats")
        if (response.ok) {
          const data = await response.json()
          setUserStats(data)
          console.log("User stats fetched:", data)
        } else {
          console.error("Failed to fetch user stats:", response.status)
        }
      } catch (error) {
        console.error("Failed to fetch user stats:", error)
      }
    }

    if (session?.user?.id) {
      fetchUserStats()
    }
  }, [session])

  const getCurrentBalance = useCallback(() => {
    return userStats?.balance ?? session?.user?.balance ?? session?.user?.walletBalance ?? 0
  }, [userStats?.balance, session?.user?.balance, session?.user?.walletBalance])

  const handlePurchaseAirtime = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()

      if (!phoneNumber || !network || !amount) {
        toast.error("Missing Information", {
          description: "Please fill in all required fields",
        })
        return
      }

      if (phoneNumber.length !== 11) {
        toast.error("Invalid Phone Number", {
          description: "Phone number must be 11 digits",
        })
        return
      }

      const purchaseAmount = Number(amount)
      if (isNaN(purchaseAmount) || purchaseAmount <= 0) {
        toast.error("Invalid Amount", {
          description: "Please enter a valid amount",
        })
        return
      }

      const currentBalance = getCurrentBalance()
      if (purchaseAmount > currentBalance) {
        toast.error("Insufficient Balance", {
          description: `Your balance (₦${currentBalance.toLocaleString()}) is insufficient for this purchase`,
          duration: 6000,
        })
        return
      }

      setLoading(true)

      // Show loading toast
      toast.loading("Processing airtime purchase...", {
        id: "airtime-loading",
        description: `Purchasing ₦${purchaseAmount.toLocaleString()} airtime for ${phoneNumber}`,
      })

      try {
        console.log("Sending airtime request:", { phoneNumber, network, amount: purchaseAmount })

        const response = await fetch("/api/vtu/airtime", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phoneNumber, network, amount: purchaseAmount }),
        })

        const data = await response.json()
        console.log("Airtime purchase response:", data)

        // Dismiss loading toast
        toast.dismiss("airtime-loading")

        if (data.success) {
          toast.success("Airtime Purchase Successful", {
            description: `₦${purchaseAmount.toLocaleString()} airtime sent to ${phoneNumber}`,
            duration: 5000,
          })

          // Reset form
          setPhoneNumber("")
          setNetwork("")
          setAmount("")

          // Update session to reflect new balance
          await update()

          // Update user stats if available
          if (data.data?.newBalance !== undefined) {
            setUserStats((prev) =>
              prev
                ? {
                    ...prev,
                    balance: data.data.newBalance,
                    totalSpent: prev.totalSpent + purchaseAmount,
                    totalTransactions: prev.totalTransactions + 1,
                  }
                : null,
            )
          }
        } else {
          toast.error("Purchase Failed", {
            description: data.message || "Unable to process airtime purchase",
            duration: 6000,
          })
        }
      } catch (error) {
        console.error("Airtime purchase error:", error)
        toast.dismiss("airtime-loading")
        toast.error("Connection Error", {
          description: "Unable to connect to service",
          duration: 6000,
        })
      } finally {
        setLoading(false)
      }
    },
    [phoneNumber, network, amount, getCurrentBalance, update],
  )

  const currentBalance = getCurrentBalance()

  return (
    <div className="min-h-screen bg-slate-900">
   <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-blue-600 rounded-full">
              <Smartphone className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">Buy Airtime</h1>
          </div>
          <p className="text-lg text-slate-400">Recharge your phone instantly for all networks</p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Balance Card */}
          <Card className="mb-6 bg-gradient-to-r from-blue-900/50 to-cyan-900/50 border-blue-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-300">Current Balance</p>
                  <p className="text-3xl font-bold text-white">₦{currentBalance.toLocaleString()}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-400">Total Spent</p>
                  <p className="text-lg font-semibold text-slate-300">
                    ₦{(userStats?.totalSpent || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Purchase Form */}
            <Card className="bg-slate-800 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Zap className="h-5 w-5" />
                  Recharge Airtime
                </CardTitle>
                <CardDescription className="text-slate-400">Fill in the details below to recharge</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePurchaseAirtime} className="space-y-6">
                  {/* Network Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="network" className="text-slate-300">
                      Network Provider
                    </Label>
                    <Select value={network} onValueChange={setNetwork} disabled={loading}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 h-12">
                        <SelectValue placeholder="Select network" />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        {networks.map((net) => (
                          <SelectItem key={net.id} value={net.id} className="text-white hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${net.color}`} />
                              {net.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Phone Number */}
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber" className="text-slate-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      placeholder="08012345678"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ""))}
                      maxLength={11}
                      required
                      disabled={loading}
                      className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 h-12 text-lg"
                    />
                  </div>

                  {/* Amount Input */}
                  <div className="space-y-2">
                    <Label htmlFor="amount" className="text-slate-300">
                      Amount (₦)
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="e.g., 100, 500, 1000"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      required
                      disabled={loading}
                      className="bg-slate-700 border-slate-600 text-white focus:border-blue-500 h-12 text-lg"
                    />
                  </div>

                  {/* Quick Amounts */}
                  <div className="space-y-2">
                    <Label className="text-slate-300">Quick Amounts</Label>
                    <div className="grid grid-cols-3 gap-3">
                      {quickAmounts.map((val) => (
                        <Button
                          key={val}
                          type="button"
                          variant="outline"
                          onClick={() => setAmount(val)}
                          disabled={loading}
                          className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 h-12 text-base"
                        >
                          ₦{val.toLocaleString()}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Purchase Summary */}
                  {amount && phoneNumber && network && (
                    <div className="bg-slate-700/50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-blue-400" />
                        <span className="font-medium text-slate-300">Purchase Summary</span>
                      </div>
                      <div className="space-y-1 text-sm text-slate-400">
                        <p>
                          Network: <span className="text-white">{networks.find((n) => n.id === network)?.name}</span>
                        </p>
                        <p>
                          Phone: <span className="text-white">{phoneNumber}</span>
                        </p>
                        <p>
                          Amount: <span className="text-white">₦{Number(amount).toLocaleString()}</span>
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={loading || !phoneNumber || !network || !amount}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white h-12 text-lg font-semibold"
                  >
                    {loading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Processing...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Zap className="h-5 w-5" />
                        Buy Airtime - ₦{Number(amount).toLocaleString() || "0"}
                      </div>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Supported Networks */}
            <Card className="bg-slate-800 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white">Supported Networks</CardTitle>
                <CardDescription className="text-slate-400">Recharge for all major Nigerian networks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                  {networks.map((net) => (
                    <div
                      key={net.id}
                      className={`p-4 border rounded-lg transition-all cursor-pointer hover:bg-slate-700 ${
                        network === net.id ? "border-blue-500 bg-blue-900/20" : "border-slate-600"
                      }`}
                      onClick={() => setNetwork(net.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded-full ${net.color}`} />
                        <span className="font-medium text-white">{net.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
