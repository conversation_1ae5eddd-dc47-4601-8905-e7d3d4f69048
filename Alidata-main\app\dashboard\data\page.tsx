"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Wifi, Smartphone, CheckCircle, Zap, Globe } from "lucide-react"
import { toast } from "sonner"

interface DataPlan {
  _id: string
  network: string
  planType: string
  size: string
  dataSize?: string
  planCode?: string
  description?: string
  validity: string
  price: number
  sellingPrice?: number
  isActive: boolean
  serviceCode: string
  vtuPricing?: {
    portalOwner: number
    reseller: number
    agent: number
    freeUser: number
  }
}

const networks = [
  { id: "mtn", name: "MTN", color: "bg-yellow-500", textColor: "text-yellow-500" },
  { id: "airtel", name: "Airtel", color: "bg-red-500", textColor: "text-red-500" },
  { id: "glo", name: "Glo", color: "bg-green-500", textColor: "text-green-500" },
  { id: "9mobile", name: "9mobile", color: "bg-green-600", textColor: "text-green-600" },
]

interface UserStats {
  balance: number
  totalSpent: number
  totalTransactions: number
}

export default function DataPage() {
  const { data: session, update } = useSession()
  const [dataPlans, setDataPlans] = useState<DataPlan[]>([])
  const [filteredPlans, setFilteredPlans] = useState<DataPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const [selectedNetwork, setSelectedNetwork] = useState<string>("")
  const [selectedPlan, setSelectedPlan] = useState<string>("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [userStats, setUserStats] = useState<UserStats | null>(null)
  const [loadingPlans, setLoadingPlans] = useState(false) // Declare loadingPlans variable

  // Fetch user stats
  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch("/api/dashboard/stats")
        if (response.ok) {
          const data = await response.json()
          setUserStats(data)
          console.log("User stats fetched:", data)
        } else {
          console.error("Failed to fetch user stats:", response.status)
        }
      } catch (error) {
        console.error("Failed to fetch user stats:", error)
      }
    }

    if (session?.user?.id) {
      fetchUserStats()
    }
  }, [session])

  useEffect(() => {
    fetchDataPlans()
  }, [])

  useEffect(() => {
    if (selectedNetwork && dataPlans.length > 0) {
      const filtered = dataPlans.filter((plan) => {
        const planNetwork = plan.network?.toLowerCase() || ""
        return planNetwork === selectedNetwork.toLowerCase() && plan.isActive
      })
      setFilteredPlans(filtered)
      setSelectedPlan("") // Reset selected plan when network changes
      console.log("Filtered plans for", selectedNetwork, ":", filtered)
    } else {
      setFilteredPlans([])
      setSelectedPlan("")
    }
  }, [selectedNetwork, dataPlans])

  const fetchDataPlans = async () => {
    setLoadingPlans(true) // Set loadingPlans to true before fetching data plans
    try {
      const response = await fetch("/api/data-plans")
      if (response.ok) {
        const result = await response.json()
        console.log("API Response:", result) // Debug log

        // Expecting { success: true, message: "...", data: [...] }
        if (result.success && Array.isArray(result.data)) {
          const plans = result.data as DataPlan[]

          // Log each plan's network field to debug
          plans.forEach((plan, index) => {
            console.log(`Plan ${index}:`, {
              network: plan.network,
              size: plan.size || plan.dataSize,
              price: plan.price || plan.sellingPrice,
            })
          })

          // Ensure all plans have required fields and valid data
          const validPlans = plans.filter((plan) => {
            const hasNetwork = plan.network && typeof plan.network === "string" && plan.network.trim() !== ""
            const hasSize = (plan.size || plan.dataSize) && typeof (plan.size || plan.dataSize) === "string"
            const hasPrice =
              typeof (plan.price || plan.sellingPrice) === "number" && (plan.price || plan.sellingPrice) > 0
            const hasValidity = plan.validity && typeof plan.validity === "string"

            if (!hasNetwork) console.log("Plan missing network:", plan)
            if (!hasSize) console.log("Plan missing size:", plan)
            if (!hasPrice) console.log("Plan missing price:", plan)
            if (!hasValidity) console.log("Plan missing validity:", plan)

            return hasNetwork && hasSize && hasPrice && hasValidity
          })

          console.log("Valid plans:", validPlans.length, "out of", plans.length)

          setDataPlans(validPlans)
        } else {
          console.error("API response format incorrect or not successful:", result)
          toast.error("Failed to fetch data plans", {
            description: result.message || "Unexpected response format from server",
            duration: 5000,
          })
          setDataPlans([]) // Clear plans on error
        }
      } else {
        console.error("Failed to fetch data plans:", response.status)
        toast.error("Failed to fetch data plans", {
          description: "Unable to load available data plans",
          duration: 5000,
        })
        setDataPlans([]) // Clear plans on error
      }
    } catch (error) {
      console.error("Error fetching data plans:", error)
      toast.error("Error loading data plans", {
        description: "Network error occurred while loading plans",
        duration: 5000,
      })
      setDataPlans([]) // Clear plans on error
    } finally {
      setLoading(false)
      setLoadingPlans(false) // Set loadingPlans to false after fetching data plans
    }
  }

  const handlePurchase = async () => {
    if (!selectedPlan || !phoneNumber) {
      toast.error("Missing information", {
        description: "Please select a data plan and enter phone number",
        duration: 4000,
      })
      return
    }

    const plan = dataPlans.find((p) => p._id === selectedPlan)
    if (!plan) {
      toast.error("Plan not found", {
        description: "Selected data plan could not be found",
        duration: 4000,
      })
      return
    }

    // Use the correct price field that matches the API expectation
    const planPrice = plan.sellingPrice || plan.price || 0

    if (planPrice <= 0) {
      toast.error("Invalid plan price", {
        description: "The selected plan has an invalid price",
        duration: 4000,
      })
      return
    }

    // Check balance if userStats is available
    const currentBalance = userStats?.balance || session?.user?.balance || session?.user?.walletBalance || 0
    if (planPrice > currentBalance) {
      toast.error("Insufficient balance", {
        description: `Your current balance is ₦${currentBalance.toLocaleString()}. Please fund your wallet.`,
        duration: 5000,
      })
      return
    }

    setPurchasing(true)

    // Show loading toast with ID for easy dismissal
    toast.loading("Processing data purchase...", {
      id: "data-purchase",
      description: `Purchasing ${plan.size || plan.dataSize} for ${phoneNumber}`,
    })

    try {
      console.log("Sending data purchase request:", {
        network: plan.network,
        planId: selectedPlan,
        phoneNumber,
        amount: planPrice,
      })

      const response = await fetch("/api/vtu/data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          network: plan.network,
          planId: selectedPlan,
          phoneNumber,
          amount: planPrice,
        }),
      })

      console.log("Response status:", response.status)
      console.log("Response ok:", response.ok)

      const result = await response.json()
      console.log("Purchase response:", result) // Debug log

      // Dismiss loading toast
      toast.dismiss("data-purchase")

      if (response.ok && result.success) {
        toast.success("Data purchase successful!", {
          description: `${plan.size || plan.dataSize} data sent to ${phoneNumber}`,
          duration: 6000,
        })

        // Reset form
        setSelectedPlan("")
        setPhoneNumber("")
        setSelectedNetwork("")

        // Update user stats if available
        if (result.data?.newBalance !== undefined) {
          setUserStats((prev) =>
            prev
              ? {
                  ...prev,
                  balance: result.data.newBalance,
                  totalSpent: prev.totalSpent + planPrice,
                  totalTransactions: prev.totalTransactions + 1,
                }
              : null,
          )

          // Update session with new balance
          await update({
            ...session,
            user: {
              ...session?.user,
              balance: result.data.newBalance,
              walletBalance: result.data.newBalance,
            },
          })
        }
      } else {
        console.error("Data purchase failed:", result)
        toast.error("Data purchase failed", {
          description: result.error || result.message || "Transaction could not be completed",
          duration: 6000,
        })
      }
    } catch (error) {
      console.error("Purchase error:", error)

      // Dismiss loading toast
      toast.dismiss("data-purchase")

      toast.error("Network error occurred", {
        description: "Please check your internet connection and try again",
        duration: 5000,
      })
    } finally {
      setPurchasing(false)
    }
  }

  // Get unique networks from data plans, filter out empty/null values
  const networksList = [...new Set(dataPlans.map((plan) => plan.network).filter(Boolean))]
  console.log("Available networks:", networksList) // Debug log
  console.log("Data plans count:", dataPlans.length)

  const currentBalance = userStats?.balance || session?.user?.balance || session?.user?.walletBalance || 0

  if (loading) {
    return (
      <div className="min-h-screen px-0 bg-slate-900 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="container rounded-4xl  px-0 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-purple-600 rounded-full">
              <Wifi className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">Buy Data</h1>
          </div>
          <p className="text-lg text-slate-400">Purchase data bundles for all networks instantly</p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Balance Card */}
          <Card className="mb-6 bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-300">Current Balance</p>
                  <p className="text-3xl font-bold text-white">₦{currentBalance.toLocaleString()}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-400">Total Spent</p>
                  <p className="text-lg font-semibold text-slate-300">
                    ₦{(userStats?.totalSpent || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Purchase Form */}
            <Card className="bg-slate-800 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Smartphone className="h-5 w-5" />
                  Purchase Data Bundle
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Select network, plan and enter phone number
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePurchase} className="space-y-6">
                  {/* Network Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="network" className="text-slate-300">
                      Network Provider ({networksList.length} available)
                    </Label>
                    <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-purple-500 h-12">
                        <SelectValue
                          placeholder={networksList.length > 0 ? "Select network" : "No networks available"}
                        />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        {networksList.length > 0 ? (
                          networksList.map((network) => (
                            <SelectItem
                              key={network}
                              value={network}
                              className="text-white hover:bg-slate-600 capitalize"
                            >
                              {network.toUpperCase()}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled className="text-slate-400">
                            No networks available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Phone Number */}
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-slate-300">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="08012345678"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ""))}
                      maxLength={11}
                      className="bg-slate-700 border-slate-600 text-white focus:border-purple-500 h-12 text-lg"
                    />
                  </div>

                  {/* Data Plan Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="plan" className="text-slate-300">
                      Data Plan ({filteredPlans.length} available)
                    </Label>
                    <Select
                      value={selectedPlan}
                      onValueChange={setSelectedPlan}
                      disabled={purchasing || !selectedNetwork || loadingPlans}
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white focus:border-purple-500 h-12">
                        <SelectValue
                          placeholder={
                            loadingPlans
                              ? "Loading plans..."
                              : !selectedNetwork
                                ? "Select network first"
                                : "Select data plan"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        {filteredPlans.map((plan) => {
                          const planSize = plan.size || plan.dataSize || "Unknown"
                          const planPrice = plan.sellingPrice || plan.price || 0
                          return (
                            <SelectItem key={plan._id} value={plan._id} className="text-white hover:bg-slate-600">
                              {planSize} - ₦{planPrice.toLocaleString()} ({plan.validity})
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    {selectedNetwork && filteredPlans.length === 0 && !loadingPlans && (
                      <p className="text-sm text-slate-400">
                        No data plans available for {selectedNetwork.toUpperCase()}
                      </p>
                    )}
                  </div>

                  {/* Purchase Summary */}
                  {selectedPlan && phoneNumber && (
                    <div className="bg-slate-700/50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-purple-400" />
                        <span className="font-medium text-slate-300">Purchase Summary</span>
                      </div>
                      <div className="space-y-1 text-sm text-slate-400">
                        <p>
                          Network:{" "}
                          <span className="text-white">{networks.find((n) => n.id === selectedNetwork)?.name}</span>
                        </p>
                        <p>
                          Phone: <span className="text-white">{phoneNumber}</span>
                        </p>
                        <p>
                          Data:{" "}
                          <span className="text-white">
                            {dataPlans.find((p) => p._id === selectedPlan)?.size || "N/A"}
                          </span>
                        </p>
                        <p>
                          Validity:{" "}
                          <span className="text-white">
                            {dataPlans.find((p) => p._id === selectedPlan)?.validity || "N/A"}
                          </span>
                        </p>
                        <p>
                          Amount:{" "}
                          <span className="text-white">
                            ₦
                            {(
                              dataPlans.find((p) => p._id === selectedPlan)?.sellingPrice ||
                              dataPlans.find((p) => p._id === selectedPlan)?.price ||
                              0
                            ).toLocaleString()}
                          </span>
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    onClick={handlePurchase}
                    disabled={purchasing || !phoneNumber || !selectedNetwork || !selectedPlan}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white h-12 text-lg font-semibold"
                  >
                    {purchasing ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Zap className="h-5 w-5" />
                        Buy Data - ₦
                        {(
                          dataPlans.find((p) => p._id === selectedPlan)?.sellingPrice ||
                          dataPlans.find((p) => p._id === selectedPlan)?.price ||
                          0
                        ).toLocaleString()}
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Available Plans */}
            <Card className="bg-slate-800 border-slate-700 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white">Available Plans</CardTitle>
                <CardDescription className="text-slate-400">
                  {selectedNetwork ? `Plans for ${selectedNetwork.toUpperCase()}` : "Select a network to view plans"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!selectedNetwork ? (
                  <div className="text-center py-8">
                    <Wifi className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                    <p className="text-slate-400">Select a network to view available plans</p>
                  </div>
                ) : loadingPlans ? (
                  <div className="text-center py-8">
                    <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
                    <p className="text-slate-400">Loading plans...</p>
                  </div>
                ) : filteredPlans.length === 0 ? (
                  <div className="text-center py-8">
                    <Globe className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                    <p className="text-slate-400">No plans available for {selectedNetwork.toUpperCase()}</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredPlans.map((plan) => (
                      <div
                        key={plan._id}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          selectedPlan === plan._id
                            ? "border-purple-500 bg-purple-900/20"
                            : "border-slate-600 hover:border-slate-500 hover:bg-slate-700/50"
                        }`}
                        onClick={() => setSelectedPlan(plan._id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-white">{plan.size || plan.dataSize}</p>
                            <p className="text-sm text-slate-400">{plan.validity}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-white">
                              ₦{(plan.sellingPrice || plan.price).toLocaleString()}
                            </p>
                            <p className="text-xs text-slate-500 capitalize">{plan.planType}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Network Cards */}
          <Card className="mt-6 bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Supported Networks</CardTitle>
              <CardDescription className="text-slate-400">Choose from all major Nigerian networks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {networks.map((net) => (
                  <div
                    key={net.id}
                    className={`p-4 border rounded-lg transition-all cursor-pointer hover:bg-slate-700 ${
                      selectedNetwork === net.id ? "border-purple-500 bg-purple-900/20" : "border-slate-600"
                    }`}
                    onClick={() => setSelectedNetwork(net.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full ${net.color}`} />
                      <span className="font-medium text-white">{net.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
