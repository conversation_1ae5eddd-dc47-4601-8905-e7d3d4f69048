"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CreditCard, Wallet, Shield, Zap, AlertCircle, CheckCircle } from "lucide-react"
import { toast } from "sonner"

interface UserStats {
  balance: number
  totalSpent: number
  totalTransactions: number
}

export default function FundWalletPage() {
  const { data: session, update } = useSession()
  const [amount, setAmount] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [userStats, setUserStats] = useState<UserStats | null>(null)

  // Fetch user stats
  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch("/api/dashboard/stats")
        if (response.ok) {
          const data = await response.json()
          setUserStats(data)
          console.log("User stats fetched:", data)
        } else {
          console.error("Failed to fetch user stats:", response.status)
        }
      } catch (error) {
        console.error("Failed to fetch user stats:", error)
      }
    }

    if (session?.user?.id) {
      fetchUserStats()
    }
  }, [session])

  const quickAmounts = [500, 1000, 2000, 5000, 10000, 20000]

  const handleFundWallet = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!amount) {
      toast.error("Please enter an amount", {
        description: "Amount is required to proceed",
      })
      return
    }

    const fundAmount = Number.parseFloat(amount)

    if (fundAmount < 100) {
      toast.error("Minimum funding amount is ₦100", {
        description: "Please enter at least ₦100",
      })
      return
    }

    if (fundAmount > 500000) {
      toast.error("Maximum funding amount is ₦500,000", {
        description: "Please enter less than ₦500,000",
      })
      return
    }

    setIsLoading(true)

    // Show loading toast
    toast.loading("Initializing payment...", {
      id: "payment-loading",
      description: `Setting up payment for ₦${fundAmount.toLocaleString()}`,
    })

    try {
      console.log("Initializing payment for amount:", fundAmount)

      const response = await fetch("/api/payment/initialize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: fundAmount,
        }),
      })

      const data = await response.json()
      console.log("Payment initialization response:", data)

      // Dismiss loading toast
      toast.dismiss("payment-loading")

      if (response.ok && data.success && data.data?.authorization_url) {
        toast.success("Redirecting to payment gateway...", {
          description: `Processing payment of ₦${fundAmount.toLocaleString()}`,
          duration: 3000,
        })

        // Small delay before redirect to show the success toast
        setTimeout(() => {
          window.location.href = data.data.authorization_url
        }, 1000)
      } else {
        console.error("Payment initialization failed:", data)
        toast.error("Payment initialization failed", {
          description: data.message || data.error || "Please try again later",
          duration: 6000,
        })
      }
    } catch (error) {
      console.error("Payment initialization error:", error)
      toast.dismiss("payment-loading")
      toast.error("Network error occurred", {
        description: "Please check your connection and try again",
        duration: 5000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen  bg-slate-900">
      <div className="container rounded-4xl px-0 py-6  ">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-green-600 rounded-full">
              <Wallet className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">Fund Wallet</h1>
          </div>
          <p className="text-lg text-slate-400">Add money to your wallet securely and instantly</p>
        </div>

        <div className="max-w-2xl mx-auto">
          {/* Balance Card */}
          <Card className="mb-6 bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-300">Current Balance</p>
                  <p className="text-3xl font-bold text-white">
                    ₦
                    {(
                      userStats?.balance ||
                      session?.user?.balance ||
                      session?.user?.walletBalance ||
                      0
                    ).toLocaleString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-400">Total Spent</p>
                  <p className="text-lg font-semibold text-slate-300">
                    ₦{(userStats?.totalSpent || 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Funding Form */}
          <Card className="bg-slate-800 border-slate-700 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <CreditCard className="h-5 w-5" />
                Add Money to Wallet
              </CardTitle>
              <CardDescription className="text-slate-400">
                Enter the amount you want to add to your wallet
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleFundWallet} className="space-y-6">
                {/* Amount Input */}
                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-slate-300">
                    Amount (₦)
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="Enter amount"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    min="100"
                    step="100"
                    required
                    className="bg-slate-700 border-slate-600 text-white focus:border-green-500 text-lg h-12"
                  />
                  <p className="text-sm text-slate-400">Minimum: ₦100, Maximum: ₦500,000</p>
                </div>

                {/* Quick Amount Buttons */}
                <div className="space-y-2">
                  <Label className="text-slate-300">Quick Select</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {quickAmounts.map((quickAmount) => (
                      <Button
                        key={quickAmount}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setAmount(quickAmount.toString())}
                        className="bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white"
                      >
                        ₦{quickAmount.toLocaleString()}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Payment Summary */}
                {amount && Number.parseFloat(amount) >= 100 && (
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="font-medium text-slate-300">Payment Summary</span>
                    </div>
                    <div className="space-y-1 text-sm text-slate-400">
                      <p>
                        Amount: <span className="text-white">₦{Number.parseFloat(amount).toLocaleString()}</span>
                      </p>
                      <p>
                        Payment Method: <span className="text-white">Paystack (Card, Bank Transfer, USSD)</span>
                      </p>
                      <p>
                        Processing: <span className="text-white">Instant</span>
                      </p>
                    </div>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading || !amount || Number.parseFloat(amount) < 100}
                  className="w-full bg-green-600 hover:bg-green-700 text-white h-12 text-lg font-semibold"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Processing...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Fund Wallet
                    </div>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Security Info */}
          <Card className="mt-6 bg-blue-900/20 border-blue-700">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-blue-400 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-blue-300 mb-1">Secure & Instant</h3>
                  <div className="text-sm text-blue-200 space-y-1">
                    <p>• Powered by Paystack - PCI DSS compliant</p>
                    <p>• Multiple payment options: Cards, Bank Transfer, USSD</p>
                    <p>• Instant wallet credit upon successful payment</p>
                    <p>• 256-bit SSL encryption for all transactions</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card className="mt-6 bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Accepted Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-slate-700/50 rounded-lg">
                  <CreditCard className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Debit Cards</p>
                </div>
                <div className="text-center p-3 bg-slate-700/50 rounded-lg">
                  <CreditCard className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Credit Cards</p>
                </div>
                <div className="text-center p-3 bg-slate-700/50 rounded-lg">
                  <Wallet className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Bank Transfer</p>
                </div>
                <div className="text-center p-3 bg-slate-700/50 rounded-lg">
                  <AlertCircle className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">USSD</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
