import type React from "react"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { Menu } from "lucide-react"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="flex-1">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 justify-between">
          <h1 className="text-lg font-semibold">Dashboard</h1>
          <div className="ml-auto">
            <SidebarTrigger className="p-2 rounded-md border border-gray-300 hover:bg-gray-100">
              <Menu className="h-5 w-5 stroke-black stroke-2" />
            </SidebarTrigger>
          </div>
        </header>
        <div className="flex-1 p-4">{children}</div>
      </main>
    </SidebarProvider>
  )
}
