"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Wallet, Activity, TrendingUp, Eye, EyeOff, Smartphone, Wifi, History } from "lucide-react"
import Link from "next/link"

interface DashboardStats {
  balance: number
  walletBalance: number
  totalTransactions: number
  totalSpent: number
  successfulTransactions: number
  pendingTransactions: number
  recentTransactions: Array<{
    id: string
    type: string
    amount: number
    status: string
    phoneNumber?: string
    network?: string
    createdAt: string
  }>
}

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showBalance, setShowBalance] = useState(true)
  const hasFetchedRef = useRef(false)

  const fetchDashboardStats = useCallback(async () => {
    if (hasFetchedRef.current || status !== "authenticated") {
      return
    }

    try {
      setLoading(true)
      const response = await fetch("/api/dashboard/stats")
      if (response.ok) {
        const data = await response.json()
        setStats(data)
        console.log("Dashboard stats loaded:", data)
        hasFetchedRef.current = true
      } else {
        console.error("Failed to load dashboard stats:", response.status)
        // Set default stats instead of showing error
        setStats({
          balance: 0,
          walletBalance: 0,
          totalTransactions: 0,
          totalSpent: 0,
          successfulTransactions: 0,
          pendingTransactions: 0,
          recentTransactions: [],
        })
        hasFetchedRef.current = true
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error)
      // Set default stats instead of showing error
      setStats({
        balance: 0,
        walletBalance: 0,
        totalTransactions: 0,
        totalSpent: 0,
        successfulTransactions: 0,
        pendingTransactions: 0,
        recentTransactions: [],
      })
      hasFetchedRef.current = true
    } finally {
      setLoading(false)
    }
  }, [status])

  useEffect(() => {
    if (status === "authenticated" && !hasFetchedRef.current) {
      fetchDashboardStats()
    } else if (status === "unauthenticated") {
      setLoading(false)
    }
  }, [status, fetchDashboardStats])

  // Get current balance from multiple sources with fallbacks
  const getCurrentBalance = useCallback(() => {
    // Priority: stats.balance > session.user.balance > session.user.walletBalance > 0
    return stats?.balance ?? session?.user?.balance ?? session?.user?.walletBalance ?? 0
  }, [stats?.balance, session?.user?.balance, session?.user?.walletBalance])

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-white" />
      </div>
    )
  }

  const currentBalance = getCurrentBalance()

  return (
    <div className="min-h-screen ">
      <div className="max-w-7xl  space-y-6">
        {/* Welcome Header */}
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Welcome back, {session?.user?.name || "User"}!</h1>
          <p className="text-slate-400">Here's what's happening with your account today.</p>
        </div>

        {/* Wallet Balance Card - Realistic Design */}
        <div className="relative">
          <div
            className="bg-gradient-to-br from-gray-900 via-black to-gray-950 rounded-2xl p-6 border border-gray-700 shadow-2xl max-w-md"
            style={{
              backgroundImage:
                "linear-gradient(45deg, rgba(255,255,255,0.05) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.05) 75%, transparent 75%, transparent)",
              backgroundSize: "20px 20px",
            }}
          >
            {/* Card Header */}
            <div className="flex items-center justify-between mb-6">
              {/* EMV Chip */}
              <div className="w-12 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md shadow-lg"></div>

              {/* Card Brand */}
              <div className="flex items-center gap-2">
                <span className="text-slate-300 text-sm font-medium tracking-wider">ALIDATA CARD</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowBalance(!showBalance)}
                  className="text-slate-400 hover:text-white transition-colors h-6 w-6"
                >
                  {showBalance ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Balance Section */}
            <div className="mb-8">
              <p className="text-slate-400 text-sm mb-1">Wallet Balance</p>
              <p className="text-white text-3xl font-bold tracking-wider">
                {showBalance ? `₦${currentBalance.toLocaleString()}` : "₦••••••"}
              </p>
            </div>

            {/* Cardholder Info */}
            <div className="flex items-end justify-between">
              <div>
                <p className="text-slate-500 text-xs uppercase tracking-wider mb-1">Cardholder</p>
                <p className="text-white text-sm font-medium uppercase tracking-wider">
                  {session?.user?.name || "USER NAME"}
                </p>
              </div>

              {/* Card Brand Logo */}
              <div className="flex items-center">
                <div className="w-8 h-8 bg-red-500 rounded-full opacity-80"></div>
                <div className="w-8 h-8 bg-yellow-500 rounded-full -ml-3 opacity-80"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <Link href="/dashboard/airtime">
            <Card className="bg-slate-800 border-slate-700 hover:bg-slate-700 transition-colors cursor-pointer">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-green-900/50 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Smartphone className="h-6 w-6 text-green-400" />
                </div>
                <p className="text-white font-medium">Buy Airtime</p>
                <p className="text-slate-400 text-sm">Top up airtime</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/data">
            <Card className="bg-slate-800 border-slate-700 hover:bg-slate-700 transition-colors cursor-pointer">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-blue-900/50 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Wifi className="h-6 w-6 text-blue-400" />
                </div>
                <p className="text-white font-medium">Buy Data</p>
                <p className="text-slate-400 text-sm">Data bundles</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/fund-wallet">
            <Card className="bg-slate-800 border-slate-700 hover:bg-slate-700 transition-colors cursor-pointer">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-purple-900/50 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Wallet className="h-6 w-6 text-purple-400" />
                </div>
                <p className="text-white font-medium">Fund Wallet</p>
                <p className="text-slate-400 text-sm">Add money</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/transactions">
            <Card className="bg-slate-800 border-slate-700 hover:bg-slate-700 transition-colors cursor-pointer">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-orange-900/50 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <History className="h-6 w-6 text-orange-400" />
                </div>
                <p className="text-white font-medium">Transactions</p>
                <p className="text-slate-400 text-sm">View history</p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Total Transactions</p>
                  <p className="text-white text-2xl font-bold">{stats?.totalTransactions || 0}</p>
                </div>
                <div className="p-3 bg-blue-900/50 rounded-lg">
                  <Activity className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Total Spent</p>
                  <p className="text-white text-2xl font-bold">₦{(stats?.totalSpent || 0).toLocaleString()}</p>
                </div>
                <div className="p-3 bg-red-900/50 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-red-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Successful</p>
                  <p className="text-white text-2xl font-bold">{stats?.successfulTransactions || 0}</p>
                </div>
                <div className="p-3 bg-green-900/50 rounded-lg">
                  <Activity className="h-6 w-6 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Pending</p>
                  <p className="text-white text-2xl font-bold">{stats?.pendingTransactions || 0}</p>
                </div>
                <div className="p-3 bg-yellow-900/50 rounded-lg">
                  <Activity className="h-6 w-6 text-yellow-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Recent Transactions</CardTitle>
            <CardDescription className="text-slate-400">Your latest transaction history</CardDescription>
          </CardHeader>
          <CardContent>
            {stats?.recentTransactions?.length ? (
              <div className="space-y-4">
                {stats.recentTransactions.slice(0, 5).map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-900/50 rounded-lg flex items-center justify-center">
                        <Activity className="h-5 w-5 text-blue-400" />
                      </div>
                      <div>
                        <p className="text-white font-medium">{transaction.type}</p>
                        <p className="text-slate-400 text-sm">
                          {transaction.phoneNumber} {transaction.network && `• ${transaction.network.toUpperCase()}`}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">₦{(transaction.amount || 0).toLocaleString()}</p>
                      <Badge
                        variant={transaction.status === "completed" ? "default" : "secondary"}
                        className={
                          transaction.status === "completed"
                            ? "bg-green-900/50 text-green-300"
                            : "bg-yellow-900/50 text-yellow-300"
                        }
                      >
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400">No transactions yet</p>
                <p className="text-slate-500 text-sm">Your transaction history will appear here</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
