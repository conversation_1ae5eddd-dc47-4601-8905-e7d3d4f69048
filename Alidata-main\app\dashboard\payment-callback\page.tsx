"use client"

import { useEffect, useState } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, Loader2 } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

export default function PaymentCallbackPage() {
  const [status, setStatus] = useState<"loading" | "success" | "failed">("loading")
  const [message, setMessage] = useState("")
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    const verifyPayment = async () => {
      const reference = searchParams.get("reference")

      if (!reference) {
        setStatus("failed")
        setMessage("No payment reference found")
        toast.error("Payment verification failed", {
          description: "No payment reference found in the URL",
          duration: 6000,
        })
        return
      }

      // Show loading toast
      const loadingToast = toast.loading("Verifying your payment...", {
        description: "Please wait while we confirm your transaction",
      })

      try {
        const response = await fetch("/api/payment/verify", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ reference }),
        })

        const data = await response.json()

        toast.dismiss(loadingToast)

        if (response.ok) {
          setStatus("success")
          setMessage(`Payment successful! ₦${data.amount.toLocaleString()} has been added to your wallet.`)

          toast.success("Payment successful! 🎉", {
            description: `₦${data.amount.toLocaleString()} has been added to your wallet`,
            duration: 6000,
          })
        } else {
          setStatus("failed")
          setMessage(data.message || "Payment verification failed")

          toast.error("Payment verification failed", {
            description: data.message || "Please contact support if money was deducted",
            duration: 8000,
          })
        }
      } catch (error) {
        toast.dismiss(loadingToast)
        setStatus("failed")
        setMessage("An error occurred while verifying payment")

        toast.error("Verification error", {
          description: "Network error occurred. Please contact support if needed.",
          duration: 8000,
        })
      }
    }

    verifyPayment()
  }, [searchParams])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          {status === "loading" && (
            <>
              <Loader2 className="h-16 w-16 animate-spin text-blue-600 mx-auto mb-4" />
              <CardTitle>Verifying Payment</CardTitle>
              <CardDescription>Please wait while we verify your payment...</CardDescription>
            </>
          )}

          {status === "success" && (
            <>
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <CardTitle className="text-green-600">Payment Successful!</CardTitle>
              <CardDescription>Your wallet has been funded successfully</CardDescription>
            </>
          )}

          {status === "failed" && (
            <>
              <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <CardTitle className="text-red-600">Payment Failed</CardTitle>
              <CardDescription>There was an issue with your payment</CardDescription>
            </>
          )}
        </CardHeader>

        <CardContent>
          <p className="mb-6">{message}</p>

          <div className="space-y-2">
            <Link href="/dashboard">
              <Button className="w-full">Go to Dashboard</Button>
            </Link>

            {status === "failed" && (
              <Link href="/dashboard/fund-wallet">
                <Button variant="outline" className="w-full bg-transparent">
                  Try Again
                </Button>
              </Link>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
