"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import {
  Receipt,
  Search,
  Filter,
  ArrowUpDown,
  Phone,
  Wifi,
  CreditCard,
  AlertCircle,
  Loader2,
  RefreshCw,
} from "lucide-react"
import { toast } from "sonner"

interface Transaction {
  _id: string
  type: string
  amount: number
  status: string
  description: string
  reference: string
  network?: string
  phoneNumber?: string
  createdAt: string
  completedAt?: string
  failureReason?: string
  metadata?: any
}

interface TransactionResponse {
  success: boolean
  message: string
  data: {
    transactions: Transaction[]
    pagination: {
      currentPage: number
      totalPages: number
      totalTransactions: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
}

export default function TransactionsPage() {
  const { data: session } = useSession()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalTransactions, setTotalTransactions] = useState(0)
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  const fetchTransactions = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        ...(typeFilter !== "all" && { type: typeFilter }),
        ...(statusFilter !== "all" && { status: statusFilter }),
      })

      const response = await fetch(`/api/user/transactions?${params}`)
      const data: TransactionResponse = await response.json()

      console.log("Transactions API Response:", data)

      if (data.success && data.data) {
        setTransactions(data.data.transactions || [])
        setCurrentPage(data.data.pagination.currentPage)
        setTotalPages(data.data.pagination.totalPages)
        setTotalTransactions(data.data.pagination.totalTransactions)
      } else {
        console.error("Failed to fetch transactions:", data.message)
        setTransactions([])
        toast.error(data.message || "Failed to load transactions")
      }
    } catch (error) {
      console.error("Error fetching transactions:", error)
      setTransactions([])
      toast.error("Network error occurred while loading transactions")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user?.id) {
      fetchTransactions(1)
    }
  }, [session, typeFilter, statusFilter])

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "airtime":
        return <Phone className="h-4 w-4" />
      case "data":
        return <Wifi className="h-4 w-4" />
      case "wallet_funding":
        return <CreditCard className="h-4 w-4" />
      default:
        return <Receipt className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-600 hover:bg-green-700 text-xs">Completed</Badge>
      case "pending":
        return <Badge className="bg-yellow-600 hover:bg-yellow-700 text-xs">Pending</Badge>
      case "failed":
        return <Badge className="bg-red-600 hover:bg-red-700 text-xs">Failed</Badge>
      default:
        return (
          <Badge variant="secondary" className="text-xs">
            {status}
          </Badge>
        )
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const filteredTransactions = transactions.filter(
    (transaction) =>
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (transaction.phoneNumber && transaction.phoneNumber.includes(searchTerm)),
  )

  if (!session) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
        <Card className="bg-slate-800 border-slate-700 w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <p className="text-white">Please log in to view your transactions</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-green-600 rounded-full">
              <Receipt className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
            </div>
            <h1 className="text-2xl sm:text-4xl font-bold text-white">Transaction History</h1>
          </div>
          <p className="text-base sm:text-lg text-slate-400 px-4">View all your transaction history and details</p>
        </div>

        {/* Filters and Search */}
        <Card className="mb-4 sm:mb-6 bg-slate-800 border-slate-700">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-white text-lg">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 lg:grid-cols-4 sm:gap-4">
              {/* Search */}
              <div className="relative sm:col-span-2 lg:col-span-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white h-11"
                />
              </div>

              {/* Type Filter */}
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white h-11">
                  <SelectValue placeholder="Transaction Type" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="airtime">Airtime</SelectItem>
                  <SelectItem value="data">Data</SelectItem>
                  <SelectItem value="wallet_funding">Wallet Funding</SelectItem>
                </SelectContent>
              </Select>

              {/* Status Filter */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white h-11">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>

              {/* Refresh Button */}
              <Button
                onClick={() => fetchTransactions(currentPage)}
                variant="outline"
                className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 h-11"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Refresh</span>
                <span className="sm:hidden">Sync</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Transactions List */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle className="text-white text-lg sm:text-xl">Recent Transactions</CardTitle>
                <CardDescription className="text-slate-400 text-sm">
                  {totalTransactions > 0
                    ? `Showing ${filteredTransactions.length} of ${totalTransactions} transactions`
                    : "No transactions found"}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <ArrowUpDown className="h-4 w-4 text-slate-400" />
                <span className="text-sm text-slate-400">Latest first</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-white" />
                <span className="ml-2 text-white">Loading transactions...</span>
              </div>
            ) : filteredTransactions.length === 0 ? (
              <div className="text-center py-12">
                <Receipt className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No Transactions Found</h3>
                <p className="text-slate-400 mb-4 px-4">
                  {searchTerm || typeFilter !== "all" || statusFilter !== "all"
                    ? "No transactions match your current filters"
                    : "You haven't made any transactions yet"}
                </p>
                {(searchTerm || typeFilter !== "all" || statusFilter !== "all") && (
                  <Button
                    onClick={() => {
                      setSearchTerm("")
                      setTypeFilter("all")
                      setStatusFilter("all")
                    }}
                    variant="outline"
                    className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {filteredTransactions.map((transaction) => (
                  <div
                    key={transaction._id}
                    className="p-4 bg-slate-700/50 rounded-lg border border-slate-600 hover:border-slate-500 transition-colors"
                  >
                    {/* Mobile Layout */}
                    <div className="sm:hidden">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-slate-600 rounded-full flex-shrink-0">
                            {getTransactionIcon(transaction.type)}
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-white text-sm leading-tight">
                              {transaction.description}
                            </h3>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0 ml-2">
                          <p className="font-bold text-white text-lg">₦{transaction.amount.toLocaleString()}</p>
                          <div className="mt-1">{getStatusBadge(transaction.status)}</div>
                        </div>
                      </div>

                      <div className="flex flex-wrap items-center gap-2 text-xs text-slate-400">
                        <span>{formatDate(transaction.createdAt)}</span>
                        {transaction.network && (
                          <>
                            <span>•</span>
                            <span className="capitalize">{transaction.network}</span>
                          </>
                        )}
                        {transaction.phoneNumber && (
                          <>
                            <span>•</span>
                            <span>{transaction.phoneNumber}</span>
                          </>
                        )}
                      </div>

                      {transaction.failureReason && (
                        <p className="text-xs text-red-400 mt-2 bg-red-900/20 p-2 rounded">
                          Reason: {transaction.failureReason}
                        </p>
                      )}
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden sm:flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-slate-600 rounded-full">{getTransactionIcon(transaction.type)}</div>
                        <div>
                          <h3 className="font-semibold text-white">{transaction.description}</h3>
                          <div className="flex items-center gap-4 text-sm text-slate-400">
                            <span>{formatDate(transaction.createdAt)}</span>
                            {transaction.network && <span className="capitalize">{transaction.network}</span>}
                            {transaction.phoneNumber && <span>{transaction.phoneNumber}</span>}
                          </div>
                          {transaction.failureReason && (
                            <p className="text-sm text-red-400 mt-1">Reason: {transaction.failureReason}</p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-white text-lg">₦{transaction.amount.toLocaleString()}</p>
                        <div className="mt-1">{getStatusBadge(transaction.status)}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row items-center justify-between mt-6 pt-6 border-t border-slate-600 gap-4">
                <div className="text-sm text-slate-400 order-2 sm:order-1">
                  Page {currentPage} of {totalPages}
                </div>
                <div className="flex gap-2 order-1 sm:order-2">
                  <Button
                    onClick={() => fetchTransactions(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                    variant="outline"
                    size="sm"
                    className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 min-w-[80px]"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={() => fetchTransactions(currentPage + 1)}
                    disabled={currentPage === totalPages || loading}
                    variant="outline"
                    size="sm"
                    className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600 min-w-[80px]"
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
