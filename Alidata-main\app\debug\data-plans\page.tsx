"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Database, RefreshCw, CheckCircle, XCircle } from "lucide-react"

interface DebugData {
  totalPlans: number
  activePlans: number
  networks: Array<{ _id: string; count: number }>
  serviceTypes: Array<{ _id: string; count: number }>
}

export default function DataPlansDebugPage() {
  const [debugData, setDebugData] = useState<DebugData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchDebugData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/data-plans/check")
      const result = await response.json()

      if (result.success) {
        setDebugData(result.data)
      } else {
        setError(result.message || "Failed to fetch debug data")
      }
    } catch (err) {
      setError("Network error occurred")
      console.error("Debug fetch error:", err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDebugData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Plans Debug</h1>
          <p className="text-muted-foreground">Check the status of data plans in the database</p>
        </div>
        <Button onClick={fetchDebugData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {error ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Error</h3>
              <p className="text-muted-foreground">{error}</p>
            </div>
          </CardContent>
        </Card>
      ) : debugData ? (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Total Plans:</span>
                <Badge variant={debugData.totalPlans > 0 ? "default" : "destructive"}>{debugData.totalPlans}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Active Plans:</span>
                <Badge variant={debugData.activePlans > 0 ? "default" : "destructive"}>{debugData.activePlans}</Badge>
              </div>
              <div className="flex items-center gap-2">
                {debugData.totalPlans > 0 ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-600">Database has data plans</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-600">No data plans found</span>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Networks</CardTitle>
              <CardDescription>Plans available per network</CardDescription>
            </CardHeader>
            <CardContent>
              {debugData.networks.length > 0 ? (
                <div className="space-y-2">
                  {debugData.networks.map((network) => (
                    <div key={network._id} className="flex items-center justify-between">
                      <span className="capitalize">{network._id}</span>
                      <Badge variant="outline">{network.count}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No networks found</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Service Types</CardTitle>
              <CardDescription>Plans available per service type</CardDescription>
            </CardHeader>
            <CardContent>
              {debugData.serviceTypes.length > 0 ? (
                <div className="space-y-2">
                  {debugData.serviceTypes.map((service) => (
                    <div key={service._id} className="flex items-center justify-between">
                      <span>{service._id}</span>
                      <Badge variant="outline">{service.count}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No service types found</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full">
                <a href="/seed-mtn">Seed MTN Data Plans</a>
              </Button>
              <Button asChild variant="outline" className="w-full bg-transparent">
                <a href="/dashboard/data">Go to Data Page</a>
              </Button>
              <Button asChild variant="outline" className="w-full bg-transparent">
                <a href="/admin/pricing">Admin Pricing</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      ) : null}
    </div>
  )
}
