import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter, Syncopate } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/components/auth-provider"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "sonner" // Import Toaster from sonner

const inter = Inter({ subsets: ["latin"] })
const syncopate = Syncopate({
  subsets: ["latin"],
  weight: ["400", "700"], // Specify weights you want to use
  variable: "--font-syncopate",
})

export const metadata: Metadata = {
  title: "Alidata VTU - Nigerian VTU Platform",
  description: "Buy airtime and data bundles for all Nigerian networks",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} ${syncopate.variable}`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <AuthProvider>
            <main className="flex-1">{children}</main>
            <Toaster
              position="top-right"
              richColors
              closeButton
              duration={5000}
              toastOptions={{
                style: {
                  background: "#1e293b",
                  border: "1px solid #334155",
                  color: "#f1f5f9",
                },
              }}
            />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
