import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Smartphone, Wifi, CreditCard, Shield, Zap, Users, TrendingUp, Star } from "lucide-react"
import Image from "next/image"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700 bg-slate-800/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 bg-blue-600 rounded-xl flex items-center justify-center">
              <Smartphone className="h-6 w-6 text-white" />
            </div>
            <div>
              <span className="text-2xl font-bold text-white">Alidata</span>
              <p className="text-xs text-slate-400">VTU Services</p>
            </div>
          </div>
          <div className="space-x-4">
            <Link href="/auth/login">
              <Button
                variant="outline"
                className="rounded-full border-slate-600 text-slate-300 hover:bg-slate-700 bg-transparent"
              >
                Login
              </Button>
            </Link>
            <Link href="/auth/register">
              <Button className="rounded-full bg-blue-600 hover:bg-blue-700">Get Started</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center relative overflow-hidden rounded-b-3xl">
        <Image
          src="/images/hero-background.jpeg"
          alt="Excited man using laptop"
          fill
          sizes="100vw"
          style={{
            objectFit: "cover",
            objectPosition: "center",
          }}
          className="absolute inset-0 z-0 opacity-100" // Adjust opacity as needed
        />
        <div className="absolute  inset-0 bg-black opacity-70 z-0"></div> {/* Dark overlay */}
        {/* Existing content of the hero section */}
        <Badge
          variant="secondary"
          className="mb-6 rounded-full px-4 py-2 bg-blue-900/50 text-blue-300 border-blue-700 relative z-10"
        >
          🚀 Nigeria's #1 VTU Platform
        </Badge>
        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight relative z-10 font-syncopate">
          {" "}
          {/* Changed font-orbitron to font-syncopate */}
          Fast & Reliable
          <span className="block text-blue-400">VTU Services</span>
        </h1>
        <p className=" text-sm md:text-xl text-slate-300 mb-10 max-w-3xl mx-auto leading-relaxed relative z-10">
          Buy airtime and data for all Nigerian networks instantly. Secure, fast, and affordable with the best rates in
          the market.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center relative z-10">
          <Link href="/auth/register">
            <Button size="lg" className="text-lg px-8 py-4 rounded-full bg-blue-600 hover:bg-blue-700">
              Start Now - It's Free
            </Button>
          </Link>
        </div>
      </section>

      {/* Bento Grid Features */}
      <section id="features" className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Why Choose Alidata?</h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Experience the future of VTU services with our cutting-edge platform
          </p>
        </div>

        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 max-w-7xl mx-auto">
          {/* Large Feature Card */}
          <Card className="md:col-span-2 lg:col-span-3 bg-blue-600 text-white border-0 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <CardHeader className="relative z-10">
              <Smartphone className="h-12 w-12 mb-4" />
              <CardTitle className="text-2xl">Instant Airtime</CardTitle>
              <CardDescription className="text-blue-100">
                Purchase airtime for all networks in seconds with our lightning-fast processing
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="flex items-center space-x-4 text-sm">
                <Badge variant="secondary" className="bg-white/20 text-white border-0">
                  MTN
                </Badge>
                <Badge variant="secondary" className="bg-white/20 text-white border-0">
                  Airtel
                </Badge>
                <Badge variant="secondary" className="bg-white/20 text-white border-0">
                  Glo
                </Badge>
                <Badge variant="secondary" className="bg-white/20 text-white border-0">
                  9mobile
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Data Bundle Card */}
          <Card className="md:col-span-2 lg:col-span-3 bg-blue-500 text-white border-0 overflow-hidden relative">
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
            <CardHeader className="relative z-10">
              <Wifi className="h-12 w-12 mb-4" />
              <CardTitle className="text-2xl">Data Bundles</CardTitle>
              <CardDescription className="text-blue-100">
                Get the best data rates with up to 15% discount on all data plans
              </CardDescription>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold mb-2">15% OFF</div>
              <p className="text-sm text-blue-100">On all data purchases</p>
            </CardContent>
          </Card>

          {/* Stats Cards */}
          <Card className="md:col-span-1 lg:col-span-2 text-center bg-slate-800 border border-slate-700 hover:shadow-lg transition-all">
            <CardHeader>
              <Users className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <CardTitle className="text-3xl font-bold text-white">4K+</CardTitle>
              <CardDescription className="text-slate-400">Happy Customers</CardDescription>
            </CardHeader>
          </Card>

          <Card className="md:col-span-1 lg:col-span-2 text-center bg-slate-800 border border-slate-700 hover:shadow-lg transition-all">
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <CardTitle className="text-3xl font-bold text-white">99.9%</CardTitle>
              <CardDescription className="text-slate-400">Success Rate</CardDescription>
            </CardHeader>
          </Card>

          <Card className="md:col-span-2 lg:col-span-2 text-center bg-slate-800 border border-slate-700 hover:shadow-lg transition-all">
            <CardHeader>
              <Zap className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <CardTitle className="text-3xl font-bold text-white">&lt;5s</CardTitle>
              <CardDescription className="text-slate-400">Average Processing Time</CardDescription>
            </CardHeader>
          </Card>

          {/* Security Card */}
          <Card className="md:col-span-2 lg:col-span-3 bg-blue-700 text-white border-0">
            <CardHeader>
              <Shield className="h-12 w-12 mb-4" />
              <CardTitle className="text-2xl">Bank-Level Security</CardTitle>
              <CardDescription className="text-blue-100">
                Your transactions are protected with 256-bit SSL encryption and secure payment processing
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Payment Card */}
          <Card className="md:col-span-2 lg:col-span-3 bg-blue-800 text-white border-0">
            <CardHeader>
              <CreditCard className="h-12 w-12 mb-4" />
              <CardTitle className="text-2xl">Easy Payments</CardTitle>
              <CardDescription className="text-blue-100">
                Fund your wallet with cards, bank transfers, or USSD. Powered by Paystack for secure transactions
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Testimonials */}
      <section className="container mx-auto px-4 py-20 bg-slate-800/50 rounded-3xl my-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">What Our Users Say</h2>
          <p className="text-xl text-slate-300">Join thousands of satisfied customers</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {[
            {
              name: "Adebayo Johnson",
              role: "Small Business Owner",
              content: "Alidata has made managing my business communications so much easier. The rates are unbeatable!",
              rating: 5,
            },
            {
              name: "Fatima Abdullahi",
              role: "Student",
              content: "I love how fast the transactions are. I can buy data for my studies without any delays.",
              rating: 5,
            },
            {
              name: "Chinedu Okafor",
              role: "Freelancer",
              content: "The admin panel is fantastic for managing my VTU business. Highly recommended!",
              rating: 5,
            },
          ].map((testimonial, index) => (
            <Card key={index} className="bg-slate-800 border border-slate-700 hover:shadow-lg transition-all">
              <CardHeader>
                <div className="flex items-center space-x-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-blue-400 text-blue-400" />
                  ))}
                </div>
                <CardDescription className="text-slate-300 text-base leading-relaxed">
                  "{testimonial.content}"
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div>
                  <p className="font-semibold text-white">{testimonial.name}</p>
                  <p className="text-sm text-slate-400">{testimonial.role}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="bg-blue-600 rounded-3xl p-12 text-white">
          <h2 className="text-4xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            Join thousands of users who trust Alidata for their VTU needs. Sign up now and get instant access to the
            best rates.
          </p>
          <Link href="/auth/register">
            <Button
              size="lg"
              variant="secondary"
              className="text-lg px-8 py-4 rounded-full bg-white text-blue-600 hover:bg-gray-100"
            >
              Create Free Account
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-800 text-white py-12 border-t border-slate-700">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-10 w-10 bg-blue-600 rounded-xl flex items-center justify-center">
                  <Smartphone className="h-6 w-6 text-white" />
                </div>
                <div>
                  <span className="text-2xl font-bold">Alidata</span>
                  <p className="text-xs text-slate-400">VTU Services</p>
                </div>
              </div>
              <p className="text-slate-400 mb-4 max-w-md">
                Nigeria's leading VTU platform providing fast, reliable, and affordable airtime and data services.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Services</h3>
              <ul className="space-y-2 text-slate-400">
                <li>Airtime Purchase</li>
                <li>Data Bundles</li>
                <li>Wallet Funding</li>
                <li>Transaction History</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-slate-400">
                <li>Help Center</li>
                <li>Contact Us</li>
                <li>API Documentation</li>
                <li>Status Page</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-400">
            <p>&copy; 2024 Alidata. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
