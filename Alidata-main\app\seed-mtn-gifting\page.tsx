"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { toast } from "@/hooks/use-toast"
import { Loader2, Database, AlertTriangle, CheckCircle, Play, Terminal } from "lucide-react"

export default function SeedAirtelGiftingPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [output, setOutput] = useState<string[]>([])
  const [isComplete, setIsComplete] = useState(false)

  const runSeedScript = async () => {
    setIsRunning(true)
    setOutput([])
    setIsComplete(false)

    try {
      const response = await fetch("/api/run-script", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          scriptName: "seed-mtn-gifting-only.js",
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to start script")
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n").filter((line) => line.trim())

          setOutput((prev) => [...prev, ...lines])
        }
      }

      setIsComplete(true)
      toast({
        title: "Success",
        description: "Airtel Gifting data plans seeded successfully!",
      })
    } catch (error) {
      console.error("Error running script:", error)
      toast({
        title: "Error",
        description: "Failed to run seeding script",
        variant: "destructive",
      })
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen bg-slate-900 p-4 sm:p-6">
      <div className="container mx-auto max-w-4xl space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold text-white flex items-center gap-2">
            <Database className="h-8 w-8 text-red-500" />
            Seed Airtel Gifting Data Plans
          </h1>
          <p className="text-slate-400">Replace all existing data plans with Airtel Gifting plans only</p>
        </div>

        {/* Warning Card */}
        <Card className="bg-red-900/20 border-red-700">
          <CardHeader>
            <CardTitle className="text-red-300 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Important Warning
            </CardTitle>
            <CardDescription className="text-red-200">
              This action will permanently delete all existing data plans and replace them with Airtel Gifting plans.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-red-200 text-sm space-y-2">
              <p>• All existing MTN, GLO, 9Mobile, and other Airtel plans will be removed</p>
              <p>• Only 18 Airtel Gifting plans will remain in the database</p>
              <p>• This action cannot be undone without re-running other seed scripts</p>
              <p>• Make sure you have a database backup if needed</p>
            </div>
          </CardContent>
        </Card>

        {/* Plan Details Card */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Airtel Gifting Plans to be Added</CardTitle>
            <CardDescription className="text-slate-400">
              18 plans will be inserted (12 active, 6 inactive)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="font-semibold text-green-400 mb-3 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Active Plans (12)
                </h3>
                <div className="space-y-2 text-sm text-slate-300">
                  <div className="flex justify-between">
                    <span>100MB - 1 Day</span>
                    <span>₦100</span>
                  </div>
                  <div className="flex justify-between">
                    <span>300MB - 2 Days</span>
                    <span>₦200</span>
                  </div>
                  <div className="flex justify-between">
                    <span>500MB - 3 Days</span>
                    <span>₦300</span>
                  </div>
                  <div className="flex justify-between">
                    <span>1GB - 7 Days</span>
                    <span>₦500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>2GB - 30 Days</span>
                    <span>₦1,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>3GB - 30 Days</span>
                    <span>₦1,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>5GB - 30 Days</span>
                    <span>₦2,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>10GB - 30 Days</span>
                    <span>₦5,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>15GB - 30 Days</span>
                    <span>₦7,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>20GB - 30 Days</span>
                    <span>₦10,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>40GB - 30 Days</span>
                    <span>₦20,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>75GB - 30 Days</span>
                    <span>₦30,000</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-yellow-400 mb-3 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Inactive Plans (6)
                </h3>
                <div className="space-y-2 text-sm text-slate-400">
                  <div className="flex justify-between">
                    <span>200MB - 1 Day</span>
                    <span>₦150</span>
                  </div>
                  <div className="flex justify-between">
                    <span>750MB - 7 Days</span>
                    <span>₦400</span>
                  </div>
                  <div className="flex justify-between">
                    <span>6GB - 30 Days</span>
                    <span>₦3,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>12GB - 30 Days</span>
                    <span>₦6,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>25GB - 30 Days</span>
                    <span>₦12,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span>100GB - 30 Days</span>
                    <span>₦40,000</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Card */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Run Seeding Script</CardTitle>
            <CardDescription className="text-slate-400">
              Click the button below to execute the Airtel Gifting data plans seeding script
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={runSeedScript}
              disabled={isRunning}
              className="w-full bg-red-600 hover:bg-red-700 text-white"
              size="lg"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Running Script...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-5 w-5" />
                  Seed Airtel Gifting Plans
                </>
              )}
            </Button>

            {isComplete && (
              <div className="flex items-center justify-center p-4 bg-green-900/20 border border-green-700 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                <span className="text-green-300 font-medium">Script completed successfully!</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Output Terminal */}
        {output.length > 0 && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Terminal className="h-5 w-5" />
                Script Output
              </CardTitle>
              <CardDescription className="text-slate-400">Real-time output from the seeding script</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-slate-900 rounded-lg p-4 font-mono text-sm max-h-96 overflow-y-auto">
                {output.map((line, index) => (
                  <div key={index} className="text-green-400 mb-1">
                    {line}
                  </div>
                ))}
                {isRunning && (
                  <div className="flex items-center text-yellow-400">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Running...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
