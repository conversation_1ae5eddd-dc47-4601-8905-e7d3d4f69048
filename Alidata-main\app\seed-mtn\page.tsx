"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, XCircle, Loader2, Database, TrendingUp, Package } from "lucide-react"

interface SeedingSummary {
  totalPlans: number
  activePlans: number
  inactivePlans: number
  smePlans: number
  giftingPlans: number
  priceRange: {
    sme: {
      min: number
      max: number
    }
    gifting: {
      min: number
      max: number
    }
  }
}

interface SeedingResponse {
  success: boolean
  message: string
  summary?: SeedingSummary
  error?: string
}

export default function SeedMTNPage() {
  const [isSeeding, setIsSeeding] = useState(false)
  const [result, setResult] = useState<SeedingResponse | null>(null)

  const handleSeedDatabase = async () => {
    setIsSeeding(true)
    setResult(null)

    try {
      const response = await fetch("/api/admin/seed-mtn", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data: SeedingResponse = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: "Failed to connect to seeding API",
        error: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setIsSeeding(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-gray-900">MTN Data Plans Seeder</h1>
          <p className="text-lg text-gray-600">Populate your database with comprehensive MTN data plans</p>
        </div>

        {/* Main Seeding Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Database className="h-6 w-6 text-blue-600" />
              Database Seeding Tool
            </CardTitle>
            <CardDescription>
              This will seed your database with 35 MTN data plans including SME and Gifting options with exact VTU
              Africa pricing
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Seeding Button */}
            <div className="text-center">
              <Button
                onClick={handleSeedDatabase}
                disabled={isSeeding}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
              >
                {isSeeding ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Seeding Database...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-5 w-5" />
                    Seed MTN Data Plans
                  </>
                )}
              </Button>
            </div>

            {/* Results */}
            {result && (
              <div className="space-y-4">
                <Separator />

                {/* Status */}
                <div className="flex items-center justify-center gap-2">
                  {result.success ? (
                    <>
                      <CheckCircle className="h-6 w-6 text-green-600" />
                      <span className="text-lg font-semibold text-green-700">Seeding Successful!</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-6 w-6 text-red-600" />
                      <span className="text-lg font-semibold text-red-700">Seeding Failed</span>
                    </>
                  )}
                </div>

                {/* Message */}
                <div className="text-center">
                  <p className={`text-sm ${result.success ? "text-green-600" : "text-red-600"}`}>{result.message}</p>
                  {result.error && <p className="text-xs text-red-500 mt-1">Error: {result.error}</p>}
                </div>

                {/* Summary Statistics */}
                {result.success && result.summary && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4 text-center">
                        <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-blue-700">{result.summary.totalPlans}</div>
                        <div className="text-sm text-blue-600">Total Plans</div>
                      </CardContent>
                    </Card>

                    <Card className="bg-green-50 border-green-200">
                      <CardContent className="p-4 text-center">
                        <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-green-700">{result.summary.activePlans}</div>
                        <div className="text-sm text-green-600">Active Plans</div>
                      </CardContent>
                    </Card>

                    <Card className="bg-orange-50 border-orange-200">
                      <CardContent className="p-4 text-center">
                        <XCircle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-orange-700">{result.summary.inactivePlans}</div>
                        <div className="text-sm text-orange-600">Inactive Plans</div>
                      </CardContent>
                    </Card>

                    <Card className="bg-purple-50 border-purple-200">
                      <CardContent className="p-4 text-center">
                        <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-purple-700">
                          {result.summary.smePlans + result.summary.giftingPlans}
                        </div>
                        <div className="text-sm text-purple-600">Plan Types</div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Plan Type Breakdown */}
                {result.success && result.summary && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-center">Plan Type Breakdown</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base flex items-center justify-between">
                            SME Plans
                            <Badge variant="secondary">{result.summary.smePlans} plans</Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm text-gray-600">
                            Price Range: ₦{result.summary.priceRange.sme.min.toLocaleString()} - ₦
                            {result.summary.priceRange.sme.max.toLocaleString()}
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base flex items-center justify-between">
                            Gifting Plans
                            <Badge variant="secondary">{result.summary.giftingPlans} plans</Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm text-gray-600">
                            Price Range: ₦{result.summary.priceRange.gifting.min.toLocaleString()} - ₦
                            {result.summary.priceRange.gifting.max.toLocaleString()}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">What This Does</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-gray-600">
              <p>• Clears existing MTN data plans from database</p>
              <p>• Inserts 35 comprehensive MTN data plans</p>
              <p>• Includes both SME and Gifting plan types</p>
              <p>• Sets up proper database indexes for performance</p>
              <p>• Uses exact VTU Africa pricing structure</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Plan Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm text-gray-600">
              <p>• SME Plans: 500MB to 40GB (9 plans)</p>
              <p>• Gifting Plans: 25MB to 110GB (26 plans)</p>
              <p>• Various validity periods: 1 day to 365 days</p>
              <p>• Competitive pricing with profit margins</p>
              <p>• Active/inactive status for plan management</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
