"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>ircle, Loader2, User } from "lucide-react"
import { toast } from "sonner"

export default function SeedPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const runSeeder = async () => {
    setLoading(true)
    setResult(null)

    const loadingToast = toast.loading("Creating admin user...", {
      description: "Setting up your Alidata application",
    })

    try {
      const response = await fetch("/api/admin/seed", {
        method: "POST",
      })

      const data = await response.json()

      toast.dismiss(loadingToast)

      if (response.ok) {
        setResult(data)
        toast.success("Admin user created successfully! 🎉", {
          description: "You can now login and manage the platform",
          duration: 6000,
        })
      } else {
        toast.error("Seeding failed", {
          description: data.message || "Please try again or check the logs",
          duration: 6000,
        })
      }
    } catch (error) {
      toast.dismiss(loadingToast)
      toast.error("Network error", {
        description: "An error occurred while seeding the database",
        duration: 6000,
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-white border-2 border-blue-100">
        <CardHeader className="text-center">
          <User className="h-16 w-16 text-blue-600 mx-auto mb-4" />
          <CardTitle className="text-2xl text-blue-600">Create Admin User</CardTitle>
          <CardDescription>Initialize your Alidata app with admin account</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {result && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center mb-3">
                <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
                <p className="font-medium text-blue-800">Admin user created successfully!</p>
              </div>
              <p className="text-sm text-blue-700 mb-4">You can now login and manage the platform.</p>

              {result?.credentials && (
                <div className="p-3 bg-white rounded border border-blue-200">
                  <p className="font-medium text-blue-800 mb-2">Admin Login Credentials:</p>
                  <p className="text-sm text-blue-700">Email: {result.credentials.admin.email}</p>
                  <p className="text-sm text-blue-700">Password: {result.credentials.admin.password}</p>
                </div>
              )}
            </div>
          )}

          <div className="text-center">
            <Button onClick={runSeeder} disabled={loading} size="lg" className="bg-blue-600 hover:bg-blue-700">
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating Admin...
                </>
              ) : (
                <>
                  <User className="h-4 w-4 mr-2" />
                  Create Admin User
                </>
              )}
            </Button>
          </div>

          <div className="text-sm text-gray-600 space-y-2">
            <p className="font-medium">This will:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Clear existing data (users, transactions, pricing)</li>
              <li>Create admin user: aliyu1234 / aliyu1234aliyu</li>
              <li>Set up clean database for production use</li>
            </ul>
            <p className="text-red-600 font-medium mt-4">⚠️ Warning: This will delete all existing data!</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
