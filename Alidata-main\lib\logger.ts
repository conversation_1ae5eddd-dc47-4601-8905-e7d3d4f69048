interface LogEntry {
  timestamp: string
  level: "info" | "warn" | "error" | "debug"
  message: string
  context?: any
  userId?: string
  ip?: string
  userAgent?: string
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === "development"

  private formatLog(entry: LogEntry): string {
    return JSON.stringify(
      {
        ...entry,
        timestamp: new Date().toISOString(),
      },
      null,
      this.isDevelopment ? 2 : 0,
    )
  }

  info(message: string, context?: any, userId?: string) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: "info",
      message,
      context,
      userId,
    }
    console.log(this.formatLog(entry))
  }

  warn(message: string, context?: any, userId?: string) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: "warn",
      message,
      context,
      userId,
    }
    console.warn(this.formatLog(entry))
  }

  error(message: string, error?: any, context?: any, userId?: string) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: "error",
      message,
      context: {
        ...context,
        error:
          error instanceof Error
            ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
              }
            : error,
      },
      userId,
    }
    console.error(this.formatLog(entry))
  }

  debug(message: string, context?: any, userId?: string) {
    if (this.isDevelopment) {
      const entry: LogEntry = {
        timestamp: new Date().toISOString(),
        level: "debug",
        message,
        context,
        userId,
      }
      console.debug(this.formatLog(entry))
    }
  }
}

export const logger = new Logger()
