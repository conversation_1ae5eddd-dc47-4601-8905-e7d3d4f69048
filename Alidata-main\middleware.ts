import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { withAuth } from "next-auth/middleware"
import { logger } from "./lib/logger"

export default withAuth(
  function middleware(request: NextRequest) {
    const startTime = Date.now()

    // Log all requests
    logger.info("Request received", {
      method: request.method,
      url: request.url,
      pathname: request.nextUrl.pathname,
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip"),
      userAgent: request.headers.get("user-agent"),
    })

    // Add CORS headers for API routes
    if (request.nextUrl.pathname.startsWith("/api/")) {
      const response = NextResponse.next()

      response.headers.set("Access-Control-Allow-Origin", "*")
      response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
      response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization")

      // Log API response time
      const processingTime = Date.now() - startTime
      logger.debug("API request processed", {
        pathname: request.nextUrl.pathname,
        method: request.method,
        processingTime,
      })

      return response
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const isAuthorized = !!token

        if (!isAuthorized) {
          logger.warn("Unauthorized access attempt", {
            pathname: req.nextUrl.pathname,
            ip: req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip"),
            userAgent: req.headers.get("user-agent"),
          })
        }

        return isAuthorized
      },
    },
  },
)

export const config = {
  matcher: ["/api/:path*", "/dashboard/:path*", "/admin/:path*"],
}
