import mongoose, { Schema, type Document } from "mongoose"

export interface IDataPricing extends Document {
  network: string
  serviceType: string
  serviceCode: string
  planId: string
  planName: string
  dataSize: string
  validity: string
  vtuPricing: {
    portalOwner: number
    reseller: number
    agent: number
    freeUser: number
  }
  apiPrice: number
  sellingPrice: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

const DataPricingSchema = new Schema<IDataPricing>(
  {
    network: {
      type: String,
      required: true,
      lowercase: true,
      enum: ["mtn", "airtel", "glo", "9mobile"],
    },
    serviceType: {
      type: String,
      required: true,
      uppercase: true,
      enum: ["SME", "GIFT", "CG", "AWOOF"],
    },
    serviceCode: {
      type: String,
      required: true,
    },
    planId: {
      type: String,
      required: true,
    },
    planName: {
      type: String,
      required: true,
    },
    dataSize: {
      type: String,
      required: true,
    },
    validity: {
      type: String,
      required: true,
    },
    vtuPricing: {
      portalOwner: {
        type: Number,
        required: true,
      },
      reseller: {
        type: Number,
        required: true,
      },
      agent: {
        type: Number,
        required: true,
      },
      freeUser: {
        type: Number,
        required: true,
      },
    },
    apiPrice: {
      type: Number,
      required: true,
    },
    sellingPrice: {
      type: Number,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Create compound indexes for better query performance
DataPricingSchema.index({ network: 1, serviceType: 1, isActive: 1 })
DataPricingSchema.index({ planId: 1 }, { unique: true })

const DataPricing = mongoose.models.DataPricing || mongoose.model<IDataPricing>("DataPricing", DataPricingSchema)

export default DataPricing
export { DataPricing }
