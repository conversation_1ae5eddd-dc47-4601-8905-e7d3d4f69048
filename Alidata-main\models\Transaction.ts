import mongoose from "mongoose"

const TransactionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: ["airtime", "data", "funding"],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "completed", "failed"],
      default: "pending",
    },
    description: {
      type: String,
      required: true,
    },
    reference: {
      type: String,
      unique: true,
    },
  },
  {
    timestamps: true,
  },
)

export default mongoose.models.Transaction || mongoose.model("Transaction", TransactionSchema)
