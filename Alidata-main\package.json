{"name": "nigerian-vtu-app", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@auth/core": "latest", "@aws-sdk/credential-providers": "latest", "@mongodb-js/zstd": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-tooltip": "^1.0.7", "@types/bcryptjs": "^2.4.6", "bcryptjs": "latest", "child_process": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "latest", "gcp-metadata": "latest", "kerberos": "latest", "lucide-react": "^0.454.0", "mongodb": "latest", "mongodb-client-encryption": "latest", "mongoose": "latest", "next": "14.2.16", "next-auth": "latest", "next-themes": "^0.2.1", "nodemailer": "latest", "path": "latest", "react": "^18", "react-dom": "^18", "snappy": "latest", "socks": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}