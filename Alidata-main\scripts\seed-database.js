// MongoDB seeding script with admin user
// Run this script to seed your database with sample data

const { MongoClient } = require("mongodb")
const bcrypt = require("bcryptjs")

const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/vtu-app"

async function seedDatabase() {
  const client = new MongoClient(MONGODB_URI)

  try {
    await client.connect()
    console.log("Connected to MongoDB")

    const db = client.db()

    // Clear existing data
    await db.collection("users").deleteMany({})
    await db.collection("transactions").deleteMany({})
    await db.collection("datapricings").deleteMany({})

    // Create only the admin user
    const adminUser = {
      firstName: "Aliyu",
      lastName: "Admin",
      email: "aliyu1234",
      phone: "08000000000",
      password: await bcrypt.hash("aliyu1234aliyu", 12),
      balance: 0,
      role: "admin",
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await db.collection("users").insertOne(adminUser)
    console.log("Admin user created")

    console.log("Database setup completed successfully!")
    console.log("\nAdmin credentials:")
    console.log("Email: aliyu1234")
    console.log("Password: aliyu1234aliyu")
  } catch (error) {
    console.error("Error setting up database:", error)
  } finally {
    await client.close()
  }
}

seedDatabase()
