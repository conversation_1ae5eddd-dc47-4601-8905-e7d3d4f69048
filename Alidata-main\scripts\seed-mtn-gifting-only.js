const { MongoClient } = require("mongodb")
const mongoose = require("mongoose")

// Get environment variables directly from process.env
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/vtu-app"

if (!MONGODB_URI) {
  throw new Error("Please define the MONGODB_URI environment variable")
}

// Define the DataPricing schema
const DataPricingSchema = new mongoose.Schema(
  {
    network: {
      type: String,
      required: true,
      enum: ["mtn", "airtel", "glo", "9mobile"],
      lowercase: true,
    },
    serviceType: {
      type: String,
      required: true,
      enum: ["SME", "GIFT", "CG", "AWOOF", "GIFTING"],
      uppercase: true,
    },
    serviceCode: {
      type: String,
      required: true,
    },
    planId: {
      type: String,
      required: true,
    },
    planName: {
      type: String,
      required: true,
    },
    dataSize: {
      type: String,
      required: true,
    },
    validity: {
      type: String,
      required: true,
    },
    vtuPricing: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
    apiPrice: {
      type: Number,
      required: true,
    },
    sellingPrice: {
      type: Number,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  },
)

// Create compound index for unique plans
DataPricingSchema.index({ network: 1, serviceType: 1, planId: 1 }, { unique: true })

const DataPricing = mongoose.models.DataPricing || mongoose.model("DataPricing", DataPricingSchema)

// Airtel Gifting Data Plans based on the provided image
const airtelGiftingPlans = [
  // Active Plans (12 plans)
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "100MB",
    price: 100,
    validity: "1 Day",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "300MB",
    price: 200,
    validity: "2 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "500MB",
    price: 300,
    validity: "3 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "1GB",
    price: 500,
    validity: "7 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "2GB",
    price: 1000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "3GB",
    price: 1500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "5GB",
    price: 2500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "10GB",
    price: 5000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "15GB",
    price: 7500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "20GB",
    price: 10000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "40GB",
    price: 20000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "75GB",
    price: 30000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "AIRTELGIFT",
  },

  // Inactive Plans (6 plans)
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "200MB",
    price: 150,
    validity: "1 Day",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "750MB",
    price: 400,
    validity: "7 Days",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "6GB",
    price: 3000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "12GB",
    price: 6000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "25GB",
    price: 12500,
    validity: "30 Days",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
  {
    network: "airtel",
    serviceType: "GIFT",
    size: "100GB",
    price: 40000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "AIRTELGIFT",
  },
]

// MTN Gifting Data Plans
const mtnGiftingPlans = [
  // Active Plans (12 plans)
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "100MB",
    price: 100,
    validity: "1 Day",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "300MB",
    price: 200,
    validity: "2 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "500MB",
    price: 300,
    validity: "3 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "1GB",
    price: 500,
    validity: "7 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "2GB",
    price: 1000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "3GB",
    price: 1500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "5GB",
    price: 2500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "10GB",
    price: 5000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "15GB",
    price: 7500,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "20GB",
    price: 10000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "40GB",
    price: 20000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "75GB",
    price: 30000,
    validity: "30 Days",
    isActive: true,
    serviceCode: "MTNGIFT",
  },

  // Inactive Plans (6 plans)
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "200MB",
    price: 150,
    validity: "1 Day",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "750MB",
    price: 400,
    validity: "7 Days",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "6GB",
    price: 3000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "12GB",
    price: 6000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "25GB",
    price: 12500,
    validity: "30 Days",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
  {
    network: "mtn",
    serviceType: "GIFT",
    size: "100GB",
    price: 40000,
    validity: "30 Days",
    isActive: false,
    serviceCode: "MTNGIFT",
  },
]

async function seedMTNGiftingOnly() {
  const client = new MongoClient(MONGODB_URI)

  try {
    await client.connect()
    console.log("🚀 Connected to MongoDB")

    const db = client.db()
    const collection = db.collection("datapricings")

    // Delete all existing data plans
    console.log("🗑️ Deleting all existing data plans...")
    const deleteResult = await collection.deleteMany({})
    console.log(`✅ Deleted ${deleteResult.deletedCount} existing data plans`)

    // Insert only MTN Gifting plans
    console.log("📊 Inserting MTN Gifting data plans...")
    const insertResult = await collection.insertMany(mtnGiftingPlans)
    console.log(`✅ Inserted ${insertResult.insertedCount} MTN Gifting data plans`)

    console.log("🎉 MTN Gifting data plans seeded successfully!")
  } catch (error) {
    console.error("❌ Error seeding MTN Gifting data plans:", error)
    throw error
  } finally {
    await client.close()
    console.log("🔌 MongoDB connection closed")
  }
}

async function seedAirtelGiftingPlans() {
  let client

  try {
    console.log("🚀 Starting Airtel Gifting Data Plans Seeding Script...")
    console.log("⚠️  This will REPLACE ALL existing data plans with Airtel Gifting plans only!")

    // Connect to MongoDB
    client = new MongoClient(MONGODB_URI)
    await client.connect()
    console.log("🚀 Connected to MongoDB")

    const db = client.db()
    const collection = db.collection("datapricings")

    // Delete all existing data plans
    console.log("🗑️ Deleting all existing data plans...")
    const deleteResult = await collection.deleteMany({})
    console.log(`✅ Deleted ${deleteResult.deletedCount} existing data plans`)

    // Insert Airtel Gifting data plans
    console.log("📊 Inserting Airtel Gifting data plans...")
    const insertResult = await collection.insertMany(airtelGiftingPlans)
    console.log(`✅ Inserted ${insertResult.insertedCount} Airtel Gifting data plans`)

    // Verify the insertion
    const totalPlans = await collection.countDocuments()
    const activePlans = await collection.countDocuments({ isActive: true })
    const inactivePlans = await collection.countDocuments({ isActive: false })

    console.log("📈 Summary:")
    console.log(`   Total plans: ${totalPlans}`)
    console.log(`   Active plans: ${activePlans}`)
    console.log(`   Inactive plans: ${inactivePlans}`)

    console.log("🎉 Airtel Gifting data plans seeded successfully!")
  } catch (error) {
    console.error("❌ Error seeding Airtel Gifting data plans:", error)
    throw error
  } finally {
    if (client) {
      await client.close()
      console.log("🔌 MongoDB connection closed")
    }
  }
}

// Run if called directly - ALWAYS run Airtel function
if (require.main === module) {
  console.log("🚀 Starting Airtel Gifting Data Plans Seeding Script...")
  console.log("⚠️  This will REPLACE ALL existing data plans with Airtel Gifting plans only!")
  console.log("")

  seedAirtelGiftingPlans()
    .then(() => {
      console.log("✅ Script completed successfully")
      process.exit(0)
    })
    .catch((error) => {
      console.error("❌ Script failed:", error)
      process.exit(1)
    })
}

// Export for use in API routes
if (typeof module !== "undefined" && module.exports) {
  module.exports = { seedAirtelGiftingPlans, seedMTNGiftingOnly }
}
