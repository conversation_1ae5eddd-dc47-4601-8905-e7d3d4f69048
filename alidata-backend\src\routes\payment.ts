import express from 'express'
import User from '../models/User'
import Transaction from '../models/Transaction'
import { logger } from '../utils/logger'
import { authenticateToken, AuthRequest } from '../middleware/auth'

const router = express.Router()

// Generate unique transaction reference
const generateReference = (userId: string) => {
  const timestamp = Date.now()
  return `alidata_${timestamp}_${userId}`
}

// Initialize payment
router.post('/initialize', authenticateToken, async (req: AuthRequest, res) => {
  const startTime = Date.now()

  try {
    const { amount } = req.body
    const userId = req.user._id

    // Validation
    if (!amount || amount < 100) {
      return res.status(400).json({
        success: false,
        message: 'Invalid amount. Minimum is ₦100'
      })
    }

    if (amount > 500000) {
      return res.status(400).json({
        success: false,
        message: 'Maximum funding amount is ₦500,000'
      })
    }

    // Get user details
    const user = await User.findById(userId)
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    // Generate transaction reference
    const reference = generateReference(userId.toString())

    // Create transaction record
    const transaction = await Transaction.create({
      userId,
      type: 'funding',
      amount,
      status: 'pending',
      description: `Wallet funding of ₦${amount.toLocaleString()}`,
      reference,
    })

    logger.info('Payment initialization started', {
      userId,
      amount,
      reference,
      transactionId: transaction._id,
    })

    // Initialize Paystack payment
    const paystackResponse = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: user.email,
        amount: amount * 100, // Paystack expects amount in kobo
        reference: reference,
        callback_url: `${process.env.FRONTEND_URL}/payment-callback`,
        metadata: {
          userId: userId.toString(),
          transactionId: transaction._id.toString(),
          type: 'funding',
        },
      }),
    })

    const paystackData: any = await paystackResponse.json()

    if (!paystackData.status) {
      // Update transaction status to failed
      await Transaction.findByIdAndUpdate(transaction._id, {
        status: 'failed',
        description: `Wallet funding failed: ${paystackData.message}`,
      })

      logger.error('Paystack initialization failed', paystackData, {
        userId,
        amount,
        reference,
      })

      return res.status(400).json({
        success: false,
        message: 'Payment initialization failed',
        error: paystackData.message,
      })
    }

    // Update transaction with Paystack reference
    await Transaction.findByIdAndUpdate(transaction._id, {
      paystackReference: paystackData.data.reference,
    })

    logger.info('Payment initialized successfully', {
      userId,
      amount,
      reference,
      paystackReference: paystackData.data.reference,
      processingTime: Date.now() - startTime,
    })

    res.json({
      success: true,
      message: 'Payment initialized successfully',
      data: paystackData.data,
    })
  } catch (error: any) {
    logger.error('Payment initialization error', error, {
      userId: req.user?._id,
      processingTime: Date.now() - startTime,
    })

    res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
})

// Verify payment
router.post('/verify', async (req, res) => {
  const startTime = Date.now()

  try {
    const { reference } = req.body

    if (!reference) {
      logger.warn('Payment verification attempt without reference', {
        ip: req.ip,
      })
      return res.status(400).json({
        success: false,
        message: 'Reference is required'
      })
    }

    logger.info('Payment verification started', {
      reference,
      ip: req.ip,
    })

    // Verify payment with Paystack
    const paystackResponse = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      headers: {
        Authorization: `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
      },
    })

    const paystackData: any = await paystackResponse.json()

    logger.debug('Paystack verification response', {
      reference,
      status: paystackData.status,
      dataStatus: paystackData.data?.status,
    })

    if (paystackData.status && paystackData.data.status === 'success') {
      const amount = paystackData.data.amount / 100 // Convert from kobo to naira
      const userId = reference.split('_')[2]

      logger.info('Payment verified successfully', {
        reference,
        amount,
        userId,
        paystackAmount: paystackData.data.amount,
      })

      // Check if transaction already exists to prevent double processing
      const existingTransaction = await Transaction.findOne({ reference })
      if (existingTransaction && existingTransaction.status === 'completed') {
        logger.warn('Duplicate payment verification attempt', {
          reference,
          existingTransactionId: existingTransaction._id,
        })
        return res.json({
          success: true,
          message: 'Payment already processed',
          amount,
        })
      }

      // Update user balance
      const user = await User.findById(userId)
      if (user) {
        const previousBalance = user.balance
        user.balance += amount
        await user.save()

        logger.info('User balance updated', {
          userId,
          email: user.email,
          previousBalance,
          newBalance: user.balance,
          amountAdded: amount,
        })

        // Update or create transaction record
        if (existingTransaction) {
          await Transaction.findByIdAndUpdate(existingTransaction._id, {
            status: 'completed',
            description: 'Wallet funding via Paystack - Completed',
          })
        } else {
          await Transaction.create({
            userId,
            type: 'funding',
            amount,
            status: 'completed',
            description: 'Wallet funding via Paystack',
            reference,
          })
        }

        logger.info('Transaction record updated/created', {
          userId,
          amount,
          reference,
          processingTime: Date.now() - startTime,
        })

        res.json({
          success: true,
          message: 'Payment verified successfully',
          amount,
        })
      } else {
        logger.error('Payment verification failed - user not found', null, {
          reference,
          userId,
          amount,
        })
        res.status(404).json({
          success: false,
          message: 'User not found'
        })
      }
    } else {
      logger.warn('Payment verification failed', {
        reference,
        paystackStatus: paystackData.status,
        paystackDataStatus: paystackData.data?.status,
        paystackMessage: paystackData.message,
      })
      res.status(400).json({
        success: false,
        message: 'Payment verification failed'
      })
    }
  } catch (error: any) {
    logger.error('Payment verification error', error, {
      processingTime: Date.now() - startTime,
      ip: req.ip,
    })
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

export default router
