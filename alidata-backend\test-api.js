// Simple API test script to verify backend functionality
const BASE_URL = 'http://localhost:5000'

async function testAPI() {
  console.log('🚀 Testing Alidata Backend API...\n')

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...')
    const healthResponse = await fetch(`${BASE_URL}/health`)
    const healthData = await healthResponse.json()
    console.log('✅ Health Check:', healthData.message)
    console.log('')

    // Test 2: Register User
    console.log('2. Testing User Registration...')
    const registerData = {
      firstName: 'Test',
      lastName: 'User',
      email: `test${Date.now()}@example.com`,
      phone: '08012345678',
      password: 'password123'
    }

    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(registerData)
    })

    const registerResult = await registerResponse.json()
    if (registerResult.success) {
      console.log('✅ User Registration successful')
      console.log('   User ID:', registerResult.data.user.id)
      console.log('   Token received:', !!registerResult.data.token)
    } else {
      console.log('❌ User Registration failed:', registerResult.message)
      return
    }

    const token = registerResult.data.token
    console.log('')

    // Test 3: Get Current User
    console.log('3. Testing Get Current User...')
    const userResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    const userData = await userResponse.json()
    if (userData.success) {
      console.log('✅ Get Current User successful')
      console.log('   Name:', userData.data.user.firstName, userData.data.user.lastName)
      console.log('   Balance:', `₦${userData.data.user.balance}`)
    } else {
      console.log('❌ Get Current User failed:', userData.message)
    }
    console.log('')

    // Test 4: Get Dashboard Stats
    console.log('4. Testing Dashboard Stats...')
    const statsResponse = await fetch(`${BASE_URL}/api/user/dashboard/stats`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    const statsData = await statsResponse.json()
    if (statsData.success) {
      console.log('✅ Dashboard Stats successful')
      console.log('   Balance:', `₦${statsData.data.balance}`)
      console.log('   Total Transactions:', statsData.data.totalTransactions)
    } else {
      console.log('❌ Dashboard Stats failed:', statsData.message)
    }
    console.log('')

    // Test 5: Get Data Plans
    console.log('5. Testing Get Data Plans...')
    const plansResponse = await fetch(`${BASE_URL}/api/data-plans`)
    const plansData = await plansResponse.json()
    
    if (plansData.success) {
      console.log('✅ Get Data Plans successful')
      console.log('   Plans found:', plansData.data.length)
      if (plansData.data.length > 0) {
        console.log('   Sample plan:', plansData.data[0].dataSize, '-', `₦${plansData.data[0].sellingPrice}`)
      }
    } else {
      console.log('❌ Get Data Plans failed:', plansData.message)
    }
    console.log('')

    // Test 6: Initialize Payment (will fail without Paystack keys)
    console.log('6. Testing Payment Initialization...')
    const paymentResponse = await fetch(`${BASE_URL}/api/payment/initialize`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}` 
      },
      body: JSON.stringify({ amount: 1000 })
    })

    const paymentData = await paymentResponse.json()
    if (paymentData.success) {
      console.log('✅ Payment Initialization successful')
      console.log('   Authorization URL received:', !!paymentData.data.authorization_url)
    } else {
      console.log('⚠️  Payment Initialization expected to fail without Paystack keys')
      console.log('   Error:', paymentData.message)
    }
    console.log('')

    console.log('🎉 API Test Complete!')
    console.log('\n📋 Summary:')
    console.log('- Authentication system: ✅ Working')
    console.log('- User management: ✅ Working')
    console.log('- Dashboard stats: ✅ Working')
    console.log('- Data plans: ✅ Working')
    console.log('- Payment system: ⚠️  Needs Paystack configuration')
    console.log('\n💡 Next steps:')
    console.log('1. Add your Paystack keys to .env file')
    console.log('2. Seed the database with data plans')
    console.log('3. Test the mobile app with this backend')

  } catch (error) {
    console.error('❌ API Test failed:', error.message)
    console.log('\n🔧 Make sure the backend server is running:')
    console.log('   cd alidata-backend && npm run dev')
  }
}

// Run the test
testAPI()
