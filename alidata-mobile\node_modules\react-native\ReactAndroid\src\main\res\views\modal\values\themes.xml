<?xml version="1.0" encoding="utf-8"?>

<resources>

  <style name="Theme.FullScreenDialog">
    <item name="android:windowNoTitle">true</item>
    <item name="android:windowIsFloating">false</item>
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    <item name="android:statusBarColor">@android:color/transparent</item>
  </style>

  <style name="Theme.FullScreenDialogAnimatedSlide" parent="Theme.FullScreenDialog">
    <item name="android:windowAnimationStyle">@style/DialogAnimationSlide</item>
  </style>

  <style name="Theme.FullScreenDialogAnimatedFade" parent="Theme.FullScreenDialog">
    <item name="android:windowAnimationStyle">@style/DialogAnimationFade</item>
  </style>

  <style name="DialogAnimationSlide">
    <item name="android:windowEnterAnimation">@anim/catalyst_slide_up</item>
    <item name="android:windowExitAnimation">@anim/catalyst_slide_down</item>
  </style>

  <style name="DialogAnimationFade">
    <item name="android:windowEnterAnimation">@anim/catalyst_fade_in</item>
    <item name="android:windowExitAnimation">@anim/catalyst_fade_out</item>
  </style>



</resources>
