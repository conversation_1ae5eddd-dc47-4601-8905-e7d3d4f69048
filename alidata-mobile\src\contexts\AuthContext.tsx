import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { authAPI, tokenManager } from '../services/api'
import { User, AuthContextType, RegisterData } from '../types'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!token

  // Initialize auth state on app start
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      setIsLoading(true)
      const storedToken = await tokenManager.getToken()
      
      if (storedToken) {
        setToken(storedToken)
        // Verify token and get user data
        const response = await authAPI.getCurrentUser()
        if (response.success) {
          setUser(response.data.user)
        } else {
          // Token is invalid, remove it
          await tokenManager.removeToken()
          setToken(null)
        }
      }
    } catch (error) {
      console.error('Auth initialization error:', error)
      // Clear invalid token
      await tokenManager.removeToken()
      setToken(null)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authAPI.login(email, password)
      
      if (response.success) {
        const { token: newToken, user: userData } = response.data
        
        // Store token securely
        await tokenManager.setToken(newToken)
        
        // Update state
        setToken(newToken)
        setUser(userData)
      } else {
        throw new Error(response.message || 'Login failed')
      }
    } catch (error: any) {
      console.error('Login error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true)
      const response = await authAPI.register(userData)
      
      if (response.success) {
        const { token: newToken, user: newUser } = response.data
        
        // Store token securely
        await tokenManager.setToken(newToken)
        
        // Update state
        setToken(newToken)
        setUser(newUser)
      } else {
        throw new Error(response.message || 'Registration failed')
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      throw new Error(error.response?.data?.message || error.message || 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)
      
      // Remove token from secure storage
      await tokenManager.removeToken()
      
      // Clear state
      setToken(null)
      setUser(null)
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      if (!token) return
      
      const response = await authAPI.getCurrentUser()
      if (response.success) {
        setUser(response.data.user)
      }
    } catch (error) {
      console.error('Refresh user error:', error)
      // If refresh fails, logout user
      await logout()
    }
  }

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
