import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useAuth } from '../../contexts/AuthContext'
import { vtuAPI } from '../../services/api'
import { DataPlan, NetworkProvider } from '../../types'

const BuyDataScreen: React.FC = () => {
  const { user, refreshUser } = useAuth()

  const [phoneNumber, setPhoneNumber] = useState('')
  const [selectedNetwork, setSelectedNetwork] = useState<string>('')
  const [selectedPlan, setSelectedPlan] = useState<string>('')
  const [dataPlans, setDataPlans] = useState<DataPlan[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingPlans, setIsLoadingPlans] = useState(false)
  const [errors, setErrors] = useState<{ phoneNumber?: string; network?: string; plan?: string }>({})

  const networks: NetworkProvider[] = [
    { id: 'mtn', name: 'MTN', color: '#FFCC00', textColor: '#000' },
    { id: 'airtel', name: 'Airtel', color: '#FF0000', textColor: '#fff' },
    { id: 'glo', name: 'Glo', color: '#00B04F', textColor: '#fff' },
    { id: '9mobile', name: '9mobile', color: '#00A651', textColor: '#fff' },
  ]

  useEffect(() => {
    if (selectedNetwork) {
      loadDataPlans()
    }
  }, [selectedNetwork])

  const loadDataPlans = async () => {
    try {
      setIsLoadingPlans(true)
      const response = await vtuAPI.getDataPlans(selectedNetwork)
      if (response.success) {
        setDataPlans(response.data)
      }
    } catch (error) {
      console.error('Load data plans error:', error)
      Alert.alert('Error', 'Failed to load data plans')
    } finally {
      setIsLoadingPlans(false)
    }
  }

  const validateForm = () => {
    const newErrors: { phoneNumber?: string; network?: string; plan?: string } = {}

    if (!phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required'
    } else if (!/^(\+234|234|0)?[789][01]\d{8}$/.test(phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid Nigerian phone number'
    }

    if (!selectedNetwork) {
      newErrors.network = 'Please select a network'
    }

    if (!selectedPlan) {
      newErrors.plan = 'Please select a data plan'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handlePurchaseData = async () => {
    if (!validateForm()) return

    const plan = dataPlans.find(p => p._id === selectedPlan)
    if (!plan) return

    if ((user?.balance || 0) < plan.sellingPrice) {
      Alert.alert(
        'Insufficient Balance',
        `You need ₦${plan.sellingPrice.toLocaleString()} to purchase this plan. Your current balance is ₦${(user?.balance || 0).toLocaleString()}.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Fund Wallet', onPress: () => {/* Navigate to fund wallet */} },
        ]
      )
      return
    }

    try {
      setIsLoading(true)
      const response = await vtuAPI.purchaseData({
        network: selectedNetwork,
        planId: selectedPlan,
        phoneNumber,
        amount: plan.sellingPrice,
      })

      if (response.success) {
        Alert.alert(
          'Purchase Successful!',
          `${plan.dataSize} data has been sent to ${phoneNumber}`,
          [{ text: 'OK', onPress: () => refreshUser() }]
        )

        // Reset form
        setPhoneNumber('')
        setSelectedNetwork('')
        setSelectedPlan('')
        setDataPlans([])
      } else {
        Alert.alert('Purchase Failed', response.message || 'Please try again')
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || 'An error occurred during purchase')
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `₦${amount.toLocaleString()}`
  }

  const selectedPlanData = dataPlans.find(p => p._id === selectedPlan)

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Balance Card */}
      <View style={styles.balanceCard}>
        <Text style={styles.balanceLabel}>Wallet Balance</Text>
        <Text style={styles.balanceAmount}>{formatCurrency(user?.balance || 0)}</Text>
      </View>

      {/* Phone Number Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Phone Number</Text>
        <TextInput
          style={[styles.input, errors.phoneNumber && styles.inputError]}
          value={phoneNumber}
          onChangeText={(value) => {
            setPhoneNumber(value)
            if (errors.phoneNumber) setErrors(prev => ({ ...prev, phoneNumber: undefined }))
          }}
          placeholder="08012345678"
          keyboardType="phone-pad"
          maxLength={11}
        />
        {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber}</Text>}
      </View>

      {/* Network Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Network</Text>
        <View style={styles.networkGrid}>
          {networks.map((network) => (
            <TouchableOpacity
              key={network.id}
              style={[
                styles.networkButton,
                { backgroundColor: network.color },
                selectedNetwork === network.id && styles.networkButtonSelected,
              ]}
              onPress={() => {
                setSelectedNetwork(network.id)
                setSelectedPlan('')
                if (errors.network) setErrors(prev => ({ ...prev, network: undefined }))
              }}
            >
              <Text style={[styles.networkText, { color: network.textColor }]}>
                {network.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {errors.network && <Text style={styles.errorText}>{errors.network}</Text>}
      </View>

      {/* Data Plans */}
      {selectedNetwork && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Data Plan</Text>
          {isLoadingPlans ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#007AFF" />
              <Text style={styles.loadingText}>Loading plans...</Text>
            </View>
          ) : (
            <View style={styles.plansContainer}>
              {dataPlans.map((plan) => (
                <TouchableOpacity
                  key={plan._id}
                  style={[
                    styles.planButton,
                    selectedPlan === plan._id && styles.planButtonSelected,
                  ]}
                  onPress={() => {
                    setSelectedPlan(plan._id)
                    if (errors.plan) setErrors(prev => ({ ...prev, plan: undefined }))
                  }}
                >
                  <View style={styles.planInfo}>
                    <Text style={styles.planSize}>{plan.dataSize}</Text>
                    <Text style={styles.planValidity}>{plan.validity}</Text>
                  </View>
                  <Text style={styles.planPrice}>{formatCurrency(plan.sellingPrice)}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
          {errors.plan && <Text style={styles.errorText}>{errors.plan}</Text>}
        </View>
      )}

      {/* Purchase Summary */}
      {selectedPlanData && (
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Purchase Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Network:</Text>
            <Text style={styles.summaryValue}>{selectedNetwork.toUpperCase()}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Phone:</Text>
            <Text style={styles.summaryValue}>{phoneNumber}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Data:</Text>
            <Text style={styles.summaryValue}>{selectedPlanData.dataSize}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Amount:</Text>
            <Text style={styles.summaryValue}>{formatCurrency(selectedPlanData.sellingPrice)}</Text>
          </View>
        </View>
      )}

      {/* Purchase Button */}
      <TouchableOpacity
        style={[
          styles.purchaseButton,
          (!phoneNumber || !selectedNetwork || !selectedPlan || isLoading) && styles.purchaseButtonDisabled,
        ]}
        onPress={handlePurchaseData}
        disabled={!phoneNumber || !selectedNetwork || !selectedPlan || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <>
            <Ionicons name="wifi" size={20} color="#fff" />
            <Text style={styles.purchaseButtonText}>
              Purchase Data {selectedPlanData ? `- ${formatCurrency(selectedPlanData.sellingPrice)}` : ''}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 20,
  },
  balanceCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  balanceLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
  },
  inputError: {
    borderColor: '#ff4444',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 14,
    marginTop: 4,
  },
  networkGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  networkButton: {
    width: '48%',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  networkButtonSelected: {
    borderWidth: 3,
    borderColor: '#007AFF',
  },
  networkText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginLeft: 8,
    color: '#666',
  },
  plansContainer: {
    gap: 8,
  },
  planButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  planButtonSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#E3F2FD',
  },
  planInfo: {
    flex: 1,
  },
  planSize: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  planValidity: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  planPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  summaryCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  purchaseButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  purchaseButtonDisabled: {
    backgroundColor: '#ccc',
  },
  purchaseButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
})

export default BuyDataScreen
