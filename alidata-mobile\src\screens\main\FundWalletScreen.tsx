import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  Linking,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useNavigation } from '@react-navigation/native'
import { useAuth } from '../../contexts/AuthContext'
import { paymentAPI } from '../../services/api'

const FundWalletScreen: React.FC = () => {
  const navigation = useNavigation()
  const { user, refreshUser } = useAuth()

  const [amount, setAmount] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{ amount?: string }>({})

  const quickAmounts = [500, 1000, 2000, 5000, 10000, 20000]

  const validateAmount = (value: string) => {
    const numValue = parseFloat(value)
    const newErrors: { amount?: string } = {}

    if (!value.trim()) {
      newErrors.amount = 'Amount is required'
    } else if (isNaN(numValue) || numValue <= 0) {
      newErrors.amount = 'Please enter a valid amount'
    } else if (numValue < 100) {
      newErrors.amount = 'Minimum amount is ₦100'
    } else if (numValue > 500000) {
      newErrors.amount = 'Maximum amount is ₦500,000'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString())
    setErrors({})
  }

  const formatCurrency = (value: number) => {
    return `₦${value.toLocaleString()}`
  }

  const handleFundWallet = async () => {
    if (!validateAmount(amount)) return

    try {
      setIsLoading(true)
      const fundAmount = parseFloat(amount)

      // Initialize payment with backend
      const response = await paymentAPI.initializePayment(fundAmount)

      if (response.success && response.data?.authorization_url) {
        // Open Paystack payment URL in browser
        const supported = await Linking.canOpenURL(response.data.authorization_url)

        if (supported) {
          await Linking.openURL(response.data.authorization_url)

          // Show success message
          Alert.alert(
            'Payment Initiated',
            `You will be redirected to complete your payment of ${formatCurrency(fundAmount)}. Please return to the app after payment.`,
            [
              {
                text: 'OK',
                onPress: () => {
                  // Navigate back and refresh user data
                  navigation.goBack()
                  refreshUser()
                },
              },
            ]
          )
        } else {
          Alert.alert('Error', 'Cannot open payment URL')
        }
      } else {
        Alert.alert(
          'Payment Failed',
          response.message || 'Failed to initialize payment. Please try again.'
        )
      }
    } catch (error: any) {
      console.error('Fund wallet error:', error)
      Alert.alert(
        'Error',
        error.message || 'An error occurred while processing your request. Please try again.'
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Current Balance */}
      <View style={styles.balanceCard}>
        <Text style={styles.balanceLabel}>Current Balance</Text>
        <Text style={styles.balanceAmount}>{formatCurrency(user?.balance || 0)}</Text>
      </View>

      {/* Amount Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Enter Amount</Text>
        <View style={styles.inputContainer}>
          <Text style={styles.currencySymbol}>₦</Text>
          <TextInput
            style={[styles.amountInput, errors.amount && styles.inputError]}
            value={amount}
            onChangeText={(value) => {
              setAmount(value)
              if (errors.amount) {
                setErrors({})
              }
            }}
            placeholder="0"
            keyboardType="numeric"
            maxLength={7}
          />
        </View>
        {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}
      </View>

      {/* Quick Amount Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Select</Text>
        <View style={styles.quickAmountsGrid}>
          {quickAmounts.map((quickAmount) => (
            <TouchableOpacity
              key={quickAmount}
              style={[
                styles.quickAmountButton,
                amount === quickAmount.toString() && styles.quickAmountButtonActive,
              ]}
              onPress={() => handleQuickAmount(quickAmount)}
            >
              <Text
                style={[
                  styles.quickAmountText,
                  amount === quickAmount.toString() && styles.quickAmountTextActive,
                ]}
              >
                {formatCurrency(quickAmount)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Payment Info */}
      <View style={styles.infoCard}>
        <View style={styles.infoRow}>
          <Ionicons name="shield-checkmark" size={20} color="#4CAF50" />
          <Text style={styles.infoText}>Secure payment powered by Paystack</Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="time" size={20} color="#2196F3" />
          <Text style={styles.infoText}>Instant wallet funding</Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="card" size={20} color="#FF9800" />
          <Text style={styles.infoText}>Pay with card, bank transfer, or USSD</Text>
        </View>
      </View>

      {/* Fund Button */}
      <TouchableOpacity
        style={[styles.fundButton, (!amount || isLoading) && styles.fundButtonDisabled]}
        onPress={handleFundWallet}
        disabled={!amount || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <>
            <Ionicons name="wallet" size={20} color="#fff" />
            <Text style={styles.fundButtonText}>
              Fund Wallet {amount ? `- ${formatCurrency(parseFloat(amount))}` : ''}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 20,
  },
  balanceCard: {
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    paddingVertical: 16,
  },
  inputError: {
    borderColor: '#ff4444',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 14,
    marginTop: 8,
  },
  quickAmountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 8,
    width: '48%',
    alignItems: 'center',
  },
  quickAmountButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  quickAmountText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  quickAmountTextActive: {
    color: '#fff',
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
    flex: 1,
  },
  fundButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  fundButtonDisabled: {
    backgroundColor: '#ccc',
  },
  fundButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
})

export default FundWalletScreen
