import React, { useEffect, useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native'
import { StackNavigationProp } from '@react-navigation/stack'
import { useAuth } from '../../contexts/AuthContext'
import { paymentAPI } from '../../services/api'
import { RootStackParamList } from '../../types'

type PaymentCallbackRouteProp = RouteProp<RootStackParamList, 'PaymentCallback'>
type PaymentCallbackNavigationProp = StackNavigationProp<RootStackParamList, 'PaymentCallback'>

const PaymentCallbackScreen: React.FC = () => {
  const navigation = useNavigation<PaymentCallbackNavigationProp>()
  const route = useRoute<PaymentCallbackRouteProp>()
  const { refreshUser } = useAuth()

  const [status, setStatus] = useState<'loading' | 'success' | 'failed'>('loading')
  const [message, setMessage] = useState('')
  const [amount, setAmount] = useState<number | null>(null)

  useEffect(() => {
    verifyPayment()
  }, [])

  const verifyPayment = async () => {
    const reference = route.params?.reference

    if (!reference) {
      setStatus('failed')
      setMessage('No payment reference found')
      return
    }

    try {
      const response = await paymentAPI.verifyPayment(reference)

      if (response.success) {
        setStatus('success')
        setAmount(response.amount)
        setMessage(`Payment successful! ₦${response.amount.toLocaleString()} has been added to your wallet.`)

        // Refresh user data to update balance
        await refreshUser()
      } else {
        setStatus('failed')
        setMessage(response.message || 'Payment verification failed')
      }
    } catch (error: any) {
      console.error('Payment verification error:', error)
      setStatus('failed')
      setMessage('An error occurred while verifying payment')
    }
  }

  const handleGoToDashboard = () => {
    navigation.navigate('Main')
  }

  const handleTryAgain = () => {
    navigation.navigate('FundWallet')
  }

  const formatCurrency = (value: number) => {
    return `₦${value.toLocaleString()}`
  }

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        {/* Status Icon */}
        <View style={styles.iconContainer}>
          {status === 'loading' && (
            <ActivityIndicator size="large" color="#007AFF" />
          )}
          {status === 'success' && (
            <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
          )}
          {status === 'failed' && (
            <Ionicons name="close-circle" size={80} color="#F44336" />
          )}
        </View>

        {/* Status Title */}
        <Text style={[
          styles.title,
          status === 'success' && styles.successTitle,
          status === 'failed' && styles.failedTitle,
        ]}>
          {status === 'loading' && 'Verifying Payment'}
          {status === 'success' && 'Payment Successful!'}
          {status === 'failed' && 'Payment Failed'}
        </Text>

        {/* Status Message */}
        <Text style={styles.message}>
          {status === 'loading' && 'Please wait while we verify your payment...'}
          {status === 'success' && message}
          {status === 'failed' && message}
        </Text>

        {/* Amount Display for Success */}
        {status === 'success' && amount && (
          <View style={styles.amountContainer}>
            <Text style={styles.amountLabel}>Amount Added</Text>
            <Text style={styles.amountValue}>{formatCurrency(amount)}</Text>
          </View>
        )}

        {/* Action Buttons */}
        {status !== 'loading' && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={handleGoToDashboard}
            >
              <Ionicons name="home" size={20} color="#fff" />
              <Text style={styles.primaryButtonText}>Go to Dashboard</Text>
            </TouchableOpacity>

            {status === 'failed' && (
              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={handleTryAgain}
              >
                <Ionicons name="refresh" size={20} color="#007AFF" />
                <Text style={styles.secondaryButtonText}>Try Again</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  successTitle: {
    color: '#4CAF50',
  },
  failedTitle: {
    color: '#F44336',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  amountContainer: {
    backgroundColor: '#E8F5E8',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
    width: '100%',
  },
  amountLabel: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
    marginBottom: 4,
  },
  amountValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    width: '100%',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    width: '100%',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
})

export default PaymentCallbackScreen
