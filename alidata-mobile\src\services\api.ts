import axios from 'axios'
import * as SecureStore from 'expo-secure-store'

// Base URL for the API - change this to your backend URL
const BASE_URL = __DEV__ ? 'http://localhost:5000' : 'https://your-production-api.com'

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Token management
const TOKEN_KEY = 'auth_token'

export const tokenManager = {
  async getToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(TOKEN_KEY)
    } catch (error) {
      console.error('Error getting token:', error)
      return null
    }
  },

  async setToken(token: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(TOKEN_KEY, token)
    } catch (error) {
      console.error('Error setting token:', error)
    }
  },

  async removeToken(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(TOKEN_KEY)
    } catch (error) {
      console.error('Error removing token:', error)
    }
  },
}

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const token = await tokenManager.getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, remove it
      await tokenManager.removeToken()
      // You might want to redirect to login screen here
    }
    return Promise.reject(error)
  }
)

// API endpoints
export const authAPI = {
  async login(email: string, password: string) {
    const response = await api.post('/api/auth/login', { email, password })
    return response.data
  },

  async register(userData: {
    firstName: string
    lastName: string
    email: string
    phone: string
    password: string
  }) {
    const response = await api.post('/api/auth/register', userData)
    return response.data
  },

  async getCurrentUser() {
    const response = await api.get('/api/auth/me')
    return response.data
  },

  async refreshToken() {
    const response = await api.post('/api/auth/refresh')
    return response.data
  },
}

export const userAPI = {
  async getTransactions(page = 1, limit = 10) {
    const response = await api.get(`/api/user/transactions?page=${page}&limit=${limit}`)
    return response.data
  },

  async getDashboardStats() {
    const response = await api.get('/api/dashboard/stats')
    return response.data
  },
}

export const paymentAPI = {
  async initializePayment(amount: number) {
    const response = await api.post('/api/payment/initialize', { amount })
    return response.data
  },

  async verifyPayment(reference: string) {
    const response = await api.post('/api/payment/verify', { reference })
    return response.data
  },
}

export const vtuAPI = {
  async getDataPlans(network?: string) {
    const response = await api.get(`/api/data-plans${network ? `?network=${network}` : ''}`)
    return response.data
  },

  async purchaseData(planData: {
    network: string
    planId: string
    phoneNumber: string
    amount: number
  }) {
    const response = await api.post('/api/vtu/data', planData)
    return response.data
  },

  async purchaseAirtime(airtimeData: {
    network: string
    phoneNumber: string
    amount: number
  }) {
    const response = await api.post('/api/vtu/airtime', airtimeData)
    return response.data
  },
}

export default api
