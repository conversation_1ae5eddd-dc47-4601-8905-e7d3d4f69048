export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  balance: number
  role: 'user' | 'admin'
}

export interface AuthResponse {
  success: boolean
  message: string
  data: {
    token: string
    user: User
  }
}

export interface Transaction {
  _id: string
  type: 'airtime' | 'data' | 'funding'
  amount: number
  status: 'pending' | 'completed' | 'failed'
  description: string
  reference: string
  network?: string
  phoneNumber?: string
  createdAt: string
  completedAt?: string
  failureReason?: string
  metadata?: any
}

export interface DataPlan {
  _id: string
  network: string
  planType: string
  size: string
  dataSize?: string
  planCode?: string
  description?: string
  validity: string
  price: number
  sellingPrice?: number
  isActive: boolean
  serviceCode: string
  planId: string
  planName: string
  vtuPricing?: {
    portalOwner: number
    reseller: number
    agent: number
    freeUser: number
  }
}

export interface DashboardStats {
  balance: number
  totalSpent: number
  totalTransactions: number
  recentTransactions: Transaction[]
}

export interface PaymentInitResponse {
  success: boolean
  data: {
    authorization_url: string
    access_code: string
    reference: string
  }
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
}

export interface PaginatedResponse<T> {
  success: boolean
  message: string
  data: {
    items: T[]
    pagination: {
      currentPage: number
      totalPages: number
      totalItems: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
}

export interface NetworkProvider {
  id: string
  name: string
  color: string
  textColor: string
}

export interface VTUPurchaseRequest {
  network: string
  phoneNumber: string
  amount: number
  planId?: string
}

export interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

export interface RegisterData {
  firstName: string
  lastName: string
  email: string
  phone: string
  password: string
}

export type RootStackParamList = {
  Auth: undefined
  Main: undefined
  Login: undefined
  Register: undefined
  Dashboard: undefined
  Profile: undefined
  Transactions: undefined
  BuyData: undefined
  BuyAirtime: undefined
  FundWallet: undefined
  PaymentCallback: { reference: string }
}

export type AuthStackParamList = {
  Login: undefined
  Register: undefined
}

export type MainTabParamList = {
  Dashboard: undefined
  Transactions: undefined
  Profile: undefined
}
